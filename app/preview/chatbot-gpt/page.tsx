"use client";

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { 
  Bot, 
  Send, 
  User, 
  RotateCcw,
  Copy,
  ThumbsUp,
  ThumbsDown,
  ArrowLeft,
  Sparkles,
  Zap,
  MessageSquare
} from 'lucide-react';
import Link from 'next/link';

// Mock streaming response simulation
const mockResponses = [
  "Hello! I'm an AI assistant powered by GPT. I'm here to help you with any questions or tasks you might have.",
  "I can help you with a wide variety of topics including:\n\n• Programming and software development\n• Writing and content creation\n• Analysis and problem-solving\n• General knowledge questions\n• Creative tasks and brainstorming\n\nWhat would you like to explore today?",
  "That's a great question! Let me think through this step by step...",
  "I'd be happy to help you with that. Here's what I recommend:",
];

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isStreaming?: boolean;
}

export default function ChatbotGptPreview() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm an AI assistant powered by GPT. How can I help you today?",
      role: 'assistant',
      timestamp: new Date(),
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const simulateStreaming = async (response: string) => {
    setStreamingContent('');
    const words = response.split(' ');
    
    for (let i = 0; i < words.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
      setStreamingContent(prev => prev + (i > 0 ? ' ' : '') + words[i]);
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Simulate AI response
    const responseContent = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isStreaming: true,
    };

    setMessages(prev => [...prev, assistantMessage]);
    
    await simulateStreaming(responseContent);
    
    setMessages(prev => 
      prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, content: responseContent, isStreaming: false }
          : msg
      )
    );
    
    setStreamingContent('');
    setIsLoading(false);
  };

  const handleRegenerate = async (messageId: string) => {
    setIsLoading(true);
    const responseContent = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: '', isStreaming: true }
          : msg
      )
    );
    
    await simulateStreaming(responseContent);
    
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: responseContent, isStreaming: false }
          : msg
      )
    );
    
    setStreamingContent('');
    setIsLoading(false);
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages, streamingContent]);

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/docs/modules/chatbot-gpt">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Docs
            </Link>
          </Button>
        </div>
        
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-lg bg-primary/10">
            <Bot className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">AI Chatbot Preview</h1>
            <p className="text-muted-foreground">Interactive demo of the GPT-powered chatbot</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="secondary">Live Demo</Badge>
          <Badge variant="outline">Streaming</Badge>
          <Badge variant="outline">GPT-4</Badge>
        </div>
      </div>

      <Tabs defaultValue="demo" className="space-y-6">
        <TabsList>
          <TabsTrigger value="demo">Live Demo</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="customization">Customization</TabsTrigger>
        </TabsList>

        {/* Demo */}
        <TabsContent value="demo">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Chat Interface */}
            <Card className="lg:col-span-2 h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/bot-avatar.png" />
                      <AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">AI Assistant</CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        Online
                      </CardDescription>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    GPT-4
                  </Badge>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 flex flex-col p-0">
                <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : ''}`}>
                        {message.role === 'assistant' && (
                          <Avatar className="h-8 w-8 mt-1">
                            <AvatarImage src="/bot-avatar.png" />
                            <AvatarFallback>
                              <Bot className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                        )}
                        
                        <div className={`flex-1 max-w-[80%] ${message.role === 'user' ? 'order-first' : ''}`}>
                          <div className={`p-3 rounded-lg ${
                            message.role === 'user' 
                              ? 'bg-primary text-primary-foreground ml-auto' 
                              : 'bg-muted'
                          }`}>
                            <div className="text-sm leading-relaxed whitespace-pre-wrap">
                              {message.isStreaming ? streamingContent : message.content}
                              {message.isStreaming && (
                                <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                              )}
                            </div>
                          </div>
                          
                          {message.role === 'assistant' && !message.isStreaming && (
                            <div className="flex items-center gap-1 mt-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => copyToClipboard(message.content)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                                onClick={() => handleRegenerate(message.id)}
                                disabled={isLoading}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                              >
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs"
                              >
                                <ThumbsDown className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>

                        {message.role === 'user' && (
                          <Avatar className="h-8 w-8 mt-1">
                            <AvatarImage src="/user-avatar.png" />
                            <AvatarFallback>
                              <User className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                {/* Input */}
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Textarea
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="Ask me anything..."
                      className="min-h-[40px] max-h-[120px] resize-none"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                    />
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={!input.trim() || isLoading}
                      size="sm"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Press Enter to send, Shift+Enter for new line
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Settings Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Configuration</CardTitle>
                <CardDescription>Chatbot settings and options</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Model Settings</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Model:</span>
                      <Badge variant="outline">GPT-4</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Temperature:</span>
                      <span>0.7</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Tokens:</span>
                      <span>1000</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Streaming:</span>
                      <Badge variant="secondary">Enabled</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Features</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span>Message History</span>
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Response Regeneration</span>
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Feedback Collection</span>
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Copy to Clipboard</span>
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">System Prompt</h4>
                  <div className="bg-muted p-3 rounded-lg text-xs">
                    You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions.
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Features */}
        <TabsContent value="features">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Streaming Responses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Real-time streaming of AI responses for better user experience and perceived performance.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Conversation Memory
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Maintains conversation context and history for coherent multi-turn conversations.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Response Regeneration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Allow users to regenerate AI responses if they're not satisfied with the initial answer.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Feedback Collection</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Built-in thumbs up/down feedback system to improve AI responses over time.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Custom System Prompts</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Configure the AI's personality and behavior with custom system prompts.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Token Usage Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Monitor and track OpenAI API token usage for cost management and optimization.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Customization */}
        <TabsContent value="customization">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customization Options</CardTitle>
                <CardDescription>
                  Tailor the AI chatbot to your specific use case
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">AI Configuration</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Custom system prompts</li>
                      <li>• Model selection (GPT-3.5, GPT-4)</li>
                      <li>• Temperature and creativity settings</li>
                      <li>• Token limits and cost control</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">UI Customization</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Custom themes and branding</li>
                      <li>• Widget positioning</li>
                      <li>• Message bubble styling</li>
                      <li>• Avatar customization</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
