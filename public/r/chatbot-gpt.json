{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "chatbot-gpt", "type": "registry:component", "title": "<PERSON> Cha<PERSON> Widget", "description": "Complete AI chatbot widget with OpenAI integration, streaming responses, conversation management, and customizable interface", "dependencies": ["date-fns"], "registryDependencies": ["button", "textarea", "card", "avatar", "badge", "scroll-area"], "files": [{"path": "lib/openai-client.ts", "type": "registry:lib"}, {"path": "types/chatbot.ts", "type": "registry:type"}, {"path": "hooks/use-chatbot.ts", "type": "registry:hook"}, {"path": "components/ui/chatbot-widget.tsx", "type": "registry:component"}, {"path": "components/ui/chatbot-message.tsx", "type": "registry:component"}, {"path": "components/ui/chatbot-input.tsx", "type": "registry:component"}, {"path": "actions/chatbot-actions.ts", "type": "registry:lib"}, {"path": "app/api/chatbot/route.ts", "type": "registry:lib", "target": "app/api/chatbot/route.ts"}]}