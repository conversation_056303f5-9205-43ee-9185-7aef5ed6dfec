{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "config-module", "type": "registry:component", "title": "Supreme Toolkit Configuration", "description": "Central configuration system for managing API keys, settings, and module configurations", "dependencies": [], "registryDependencies": [], "files": [{"path": "config.tsx", "content": "/**\n * Supreme Toolkit Configuration\n * \n * This file contains all configuration for Supreme Toolkit modules.\n * Each module can access its configuration through this centralized system.\n */\n\n// ============================================================================\n// TYPE DEFINITIONS\n// ============================================================================\n\nexport interface AuthConfig {\n  providers: ('google' | 'github' | 'email')[];\n  redirectUrl?: string;\n  sessionDuration?: number; // in seconds\n}\n\nexport interface StripeConfig {\n  apiKey: string;\n  productIds: string[];\n  successUrl?: string;\n  cancelUrl?: string;\n  webhookSecret?: string;\n}\n\nexport interface ResendConfig {\n  apiKey: string;\n  fromEmail?: string;\n  fromName?: string;\n}\n\nexport interface OpenAIConfig {\n  apiKey: string;\n  model?: string;\n  maxTokens?: number;\n  temperature?: number;\n}\n\nexport interface SupabaseConfig {\n  url: string;\n  anonKey: string;\n  serviceRoleKey?: string;\n}\n\nexport interface CloudinaryConfig {\n  cloudName: string;\n  apiKey: string;\n  apiSecret: string;\n  uploadPreset?: string;\n}\n\nexport interface WaitlistConfig {\n  successRedirect: string;\n  emailNotifications?: boolean;\n  autoApprove?: boolean;\n}\n\nexport interface RichTextEditorConfig {\n  saveEndpoint: string;\n  autoSave?: boolean;\n  autoSaveInterval?: number; // in milliseconds\n}\n\nexport interface AnalyticsConfig {\n  trackingId?: string;\n  enablePageViews?: boolean;\n  enableEvents?: boolean;\n}\n\nexport interface ChatConfig {\n  provider: 'supabase' | 'pusher';\n  maxMessageLength?: number;\n  enableTypingIndicators?: boolean;\n  enableFileUploads?: boolean;\n}\n\nexport interface SupportConfig {\n  defaultPriority: 'low' | 'medium' | 'high' | 'urgent';\n  autoAssignment?: boolean;\n  emailNotifications?: boolean;\n}\n\nexport interface WebhookConfig {\n  enableLogging?: boolean;\n  retryAttempts?: number;\n  timeout?: number; // in milliseconds\n}\n\nexport interface FeedbackConfig {\n  categories?: string[];\n  enableScreenshots?: boolean;\n  emailNotifications?: boolean;\n}\n\nexport interface NewsletterConfig {\n  provider: 'mailerlite' | 'postmark' | 'resend';\n  doubleOptIn?: boolean;\n  welcomeEmail?: boolean;\n}\n\n// ============================================================================\n// MAIN CONFIG INTERFACE\n// ============================================================================\n\nexport interface ToolkitConfig {\n  // Core modules\n  auth?: AuthConfig;\n  stripe?: StripeConfig;\n  resend?: ResendConfig;\n  \n  // Advanced modules\n  openai?: OpenAIConfig;\n  supabase?: SupabaseConfig;\n  cloudinary?: CloudinaryConfig;\n  \n  // Feature modules\n  waitlist?: WaitlistConfig;\n  richTextEditor?: RichTextEditorConfig;\n  analytics?: AnalyticsConfig;\n  chat?: ChatConfig;\n  support?: SupportConfig;\n  webhook?: WebhookConfig;\n  feedback?: FeedbackConfig;\n  newsletter?: NewsletterConfig;\n}\n\n// ============================================================================\n// DEFAULT CONFIGURATION\n// ============================================================================\n\nexport const defaultConfig: Partial<ToolkitConfig> = {\n  auth: {\n    providers: ['email'],\n    sessionDuration: 30 * 24 * 60 * 60, // 30 days\n  },\n  waitlist: {\n    successRedirect: '/thanks',\n    emailNotifications: true,\n    autoApprove: false,\n  },\n  richTextEditor: {\n    saveEndpoint: '/api/save-content',\n    autoSave: true,\n    autoSaveInterval: 5000, // 5 seconds\n  },\n  analytics: {\n    enablePageViews: true,\n    enableEvents: true,\n  },\n  chat: {\n    provider: 'supabase',\n    maxMessageLength: 1000,\n    enableTypingIndicators: true,\n    enableFileUploads: false,\n  },\n  support: {\n    defaultPriority: 'medium',\n    autoAssignment: false,\n    emailNotifications: true,\n  },\n  webhook: {\n    enableLogging: true,\n    retryAttempts: 3,\n    timeout: 30000, // 30 seconds\n  },\n  feedback: {\n    categories: ['bug', 'feature', 'improvement', 'other'],\n    enableScreenshots: true,\n    emailNotifications: true,\n  },\n  newsletter: {\n    provider: 'resend',\n    doubleOptIn: true,\n    welcomeEmail: true,\n  },\n};\n\n// ============================================================================\n// USER CONFIGURATION\n// ============================================================================\n\n/**\n * Supreme Toolkit Configuration\n * \n * Configure your Supreme Toolkit modules here.\n * Only include the modules you're using in your project.\n */\nexport const toolkitConfig: ToolkitConfig = {\n  // waitlist-component: {\n  //   // Add your waitlist-component configuration here\n  // },\n\n  // Authentication with betterAuth\n  auth: {\n    providers: ['google', 'email', 'github'],\n  },\n  \n  // Stripe payments\n  stripe: {\n    apiKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,\n    productIds: ['prod_example1', 'prod_example2'],\n    successUrl: '/payment/success',\n    cancelUrl: '/payment/cancel',\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,\n  },\n  \n  // Email with Resend\n  resend: {\n    apiKey: process.env.RESEND_API_KEY!,\n    fromEmail: '<EMAIL>',\n    fromName: 'Your App',\n  },\n  \n  // OpenAI for chatbot\n  openai: {\n    apiKey: process.env.OPENAI_API_KEY!,\n    model: 'gpt-4',\n    maxTokens: 1000,\n    temperature: 0.7,\n  },\n  \n  // Supabase for realtime features\n  supabase: {\n    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n  \n  // Cloudinary for image uploads\n  cloudinary: {\n    cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME!,\n    apiKey: process.env.CLOUDINARY_API_KEY!,\n    apiSecret: process.env.CLOUDINARY_API_SECRET!,\n    uploadPreset: 'supreme-toolkit',\n  },\n  \n  // Waitlist configuration\n  waitlist: {\n    successRedirect: '/thanks',\n    emailNotifications: true,\n  },\n  \n  // Rich text editor\n  richTextEditor: {\n    saveEndpoint: '/api/save-content',\n    autoSave: true,\n    autoSaveInterval: 3000, // 3 seconds\n  },\n  \n  // Analytics\n  analytics: {\n    trackingId: process.env.NEXT_PUBLIC_GA_TRACKING_ID,\n    enablePageViews: true,\n    enableEvents: true,\n  },\n};\n\n// ============================================================================\n// CONFIG UTILITIES\n// ============================================================================\n\n/**\n * Get configuration for a specific module\n */\nexport function getModuleConfig<K extends keyof ToolkitConfig>(\n  module: K\n): ToolkitConfig[K] {\n  const userConfig = toolkitConfig[module];\n  const defaultModuleConfig = defaultConfig[module];\n  \n  if (!userConfig && !defaultModuleConfig) {\n    throw new Error(`Configuration for module \"${module}\" not found`);\n  }\n  \n  // Merge user config with defaults\n  return {\n    ...defaultModuleConfig,\n    ...userConfig,\n  } as ToolkitConfig[K];\n}\n\n/**\n * Validate that required environment variables are set\n */\nexport function validateConfig(): { isValid: boolean; errors: string[] } {\n  const errors: string[] = [];\n  \n  // Check Stripe config if enabled\n  if (toolkitConfig.stripe) {\n    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {\n      errors.push('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is required for Stripe module');\n    }\n    if (!process.env.STRIPE_SECRET_KEY) {\n      errors.push('STRIPE_SECRET_KEY is required for Stripe module');\n    }\n  }\n  \n  // Check Resend config if enabled\n  if (toolkitConfig.resend) {\n    if (!process.env.RESEND_API_KEY) {\n      errors.push('RESEND_API_KEY is required for Resend module');\n    }\n  }\n  \n  // Check OpenAI config if enabled\n  if (toolkitConfig.openai) {\n    if (!process.env.OPENAI_API_KEY) {\n      errors.push('OPENAI_API_KEY is required for OpenAI module');\n    }\n  }\n  \n  // Check Supabase config if enabled\n  if (toolkitConfig.supabase) {\n    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {\n      errors.push('NEXT_PUBLIC_SUPABASE_URL is required for Supabase module');\n    }\n    if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {\n      errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required for Supabase module');\n    }\n  }\n  \n  // Check Cloudinary config if enabled\n  if (toolkitConfig.cloudinary) {\n    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME) {\n      errors.push('NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME is required for Cloudinary module');\n    }\n    if (!process.env.CLOUDINARY_API_KEY) {\n      errors.push('CLOUDINARY_API_KEY is required for Cloudinary module');\n    }\n    if (!process.env.CLOUDINARY_API_SECRET) {\n      errors.push('CLOUDINARY_API_SECRET is required for Cloudinary module');\n    }\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\n/**\n * Check if a module is enabled in the configuration\n */\nexport function isModuleEnabled(module: keyof ToolkitConfig): boolean {\n  return toolkitConfig[module] !== undefined;\n}\n", "type": "registry:lib", "target": "config.tsx"}, {"path": "types/index.ts", "content": "/**\n * Supreme Toolkit Global Types\n * \n * This file contains shared TypeScript definitions used across\n * all Supreme Toolkit modules.\n */\n\n// ============================================================================\n// COMMON TYPES\n// ============================================================================\n\nexport type Status = 'idle' | 'loading' | 'success' | 'error';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  offset?: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n}\n\n// ============================================================================\n// USER & AUTH TYPES\n// ============================================================================\n\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  avatar?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  emailVerified?: boolean;\n  role?: 'user' | 'admin' | 'moderator';\n}\n\nexport interface Session {\n  id: string;\n  userId: string;\n  user: User;\n  expiresAt: Date;\n  createdAt: Date;\n}\n\nexport interface AuthProvider {\n  id: string;\n  name: string;\n  type: 'oauth' | 'email' | 'magic-link';\n  enabled: boolean;\n}\n\n// ============================================================================\n// PAYMENT TYPES\n// ============================================================================\n\nexport interface Product {\n  id: string;\n  name: string;\n  description?: string;\n  price: number;\n  currency: string;\n  interval?: 'month' | 'year' | 'week' | 'day';\n  features?: string[];\n  popular?: boolean;\n  stripePriceId?: string;\n}\n\nexport interface Subscription {\n  id: string;\n  userId: string;\n  productId: string;\n  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'trialing';\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  cancelAtPeriodEnd: boolean;\n  stripeSubscriptionId?: string;\n}\n\nexport interface Payment {\n  id: string;\n  userId: string;\n  amount: number;\n  currency: string;\n  status: 'pending' | 'succeeded' | 'failed' | 'canceled';\n  description?: string;\n  stripePaymentIntentId?: string;\n  createdAt: Date;\n}\n\n// ============================================================================\n// CHAT TYPES\n// ============================================================================\n\nexport interface ChatMessage {\n  id: string;\n  channelId: string;\n  userId: string;\n  user: User;\n  content: string;\n  type: 'text' | 'image' | 'file' | 'system';\n  createdAt: Date;\n  updatedAt?: Date;\n  edited?: boolean;\n  replyTo?: string;\n  attachments?: ChatAttachment[];\n}\n\nexport interface ChatChannel {\n  id: string;\n  name: string;\n  description?: string;\n  type: 'public' | 'private' | 'direct';\n  createdBy: string;\n  members: string[];\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface ChatAttachment {\n  id: string;\n  name: string;\n  url: string;\n  type: string;\n  size: number;\n}\n\nexport interface TypingIndicator {\n  userId: string;\n  channelId: string;\n  user: User;\n  timestamp: Date;\n}\n\n// ============================================================================\n// SUPPORT TYPES\n// ============================================================================\n\nexport interface SupportTicket {\n  id: string;\n  userId: string;\n  user: User;\n  title: string;\n  description: string;\n  status: 'open' | 'in_progress' | 'resolved' | 'closed';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  category?: string;\n  assignedTo?: string;\n  assignee?: User;\n  createdAt: Date;\n  updatedAt: Date;\n  resolvedAt?: Date;\n}\n\nexport interface TicketMessage {\n  id: string;\n  ticketId: string;\n  userId: string;\n  user: User;\n  content: string;\n  type: 'message' | 'status_change' | 'assignment' | 'system';\n  createdAt: Date;\n  attachments?: TicketAttachment[];\n}\n\nexport interface TicketAttachment {\n  id: string;\n  name: string;\n  url: string;\n  type: string;\n  size: number;\n}\n\n// ============================================================================\n// CONTENT TYPES\n// ============================================================================\n\nexport interface RichTextContent {\n  id: string;\n  userId: string;\n  title?: string;\n  content: string; // JSON string from Tiptap\n  htmlContent?: string; // Rendered HTML\n  version: number;\n  published: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  publishedAt?: Date;\n}\n\nexport interface ContentVersion {\n  id: string;\n  contentId: string;\n  content: string;\n  version: number;\n  createdAt: Date;\n  createdBy: string;\n}\n\n// ============================================================================\n// ANALYTICS TYPES\n// ============================================================================\n\nexport interface AnalyticsEvent {\n  id: string;\n  userId?: string;\n  sessionId: string;\n  event: string;\n  properties?: Record<string, any>;\n  timestamp: Date;\n  url?: string;\n  userAgent?: string;\n  ip?: string;\n}\n\nexport interface PageView {\n  id: string;\n  userId?: string;\n  sessionId: string;\n  url: string;\n  title?: string;\n  referrer?: string;\n  timestamp: Date;\n  duration?: number;\n}\n\n// ============================================================================\n// WAITLIST TYPES\n// ============================================================================\n\nexport interface WaitlistEntry {\n  id: string;\n  email: string;\n  name?: string;\n  referralCode?: string;\n  referredBy?: string;\n  status: 'pending' | 'approved' | 'rejected';\n  position?: number;\n  createdAt: Date;\n  approvedAt?: Date;\n  notifiedAt?: Date;\n}\n\n// ============================================================================\n// FEEDBACK TYPES\n// ============================================================================\n\nexport interface FeedbackEntry {\n  id: string;\n  userId?: string;\n  user?: User;\n  type: 'bug' | 'feature' | 'improvement' | 'other';\n  title: string;\n  description: string;\n  url?: string;\n  screenshot?: string;\n  status: 'open' | 'in_review' | 'planned' | 'completed' | 'rejected';\n  priority?: 'low' | 'medium' | 'high';\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// ============================================================================\n// WEBHOOK TYPES\n// ============================================================================\n\nexport interface WebhookEvent {\n  id: string;\n  source: string;\n  event: string;\n  payload: Record<string, any>;\n  headers: Record<string, string>;\n  timestamp: Date;\n  processed: boolean;\n  processedAt?: Date;\n  error?: string;\n  retryCount: number;\n}\n\nexport interface WebhookEndpoint {\n  id: string;\n  url: string;\n  events: string[];\n  secret?: string;\n  active: boolean;\n  createdAt: Date;\n  lastTriggered?: Date;\n}\n\n// ============================================================================\n// NEWSLETTER TYPES\n// ============================================================================\n\nexport interface NewsletterSubscriber {\n  id: string;\n  email: string;\n  name?: string;\n  status: 'subscribed' | 'unsubscribed' | 'pending';\n  tags?: string[];\n  subscribedAt: Date;\n  unsubscribedAt?: Date;\n  source?: string;\n}\n\nexport interface NewsletterCampaign {\n  id: string;\n  name: string;\n  subject: string;\n  content: string;\n  status: 'draft' | 'scheduled' | 'sent';\n  scheduledAt?: Date;\n  sentAt?: Date;\n  recipients: number;\n  opens?: number;\n  clicks?: number;\n  createdAt: Date;\n}\n\n// ============================================================================\n// FILE UPLOAD TYPES\n// ============================================================================\n\nexport interface UploadedFile {\n  id: string;\n  userId?: string;\n  name: string;\n  originalName: string;\n  url: string;\n  type: string;\n  size: number;\n  width?: number;\n  height?: number;\n  createdAt: Date;\n}\n\nexport interface UploadProgress {\n  loaded: number;\n  total: number;\n  percentage: number;\n}\n\n// ============================================================================\n// UTILITY TYPES\n// ============================================================================\n\nexport type DeepPartial<T> = {\n  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];\n};\n\nexport type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;\n\nexport type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport type WithTimestamps<T> = T & {\n  createdAt: Date;\n  updatedAt: Date;\n};\n\nexport type WithId<T> = T & {\n  id: string;\n};\n\nexport type CreateInput<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;\n\nexport type UpdateInput<T> = Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;\n\n// ============================================================================\n// EVENT TYPES (for Server Actions)\n// ============================================================================\n\nexport interface BaseEventParams {\n  timestamp: Date;\n  userId?: string;\n  sessionId?: string;\n}\n\nexport interface AuthEventParams extends BaseEventParams {\n  user: User;\n  provider?: string;\n}\n\nexport interface PaymentEventParams extends BaseEventParams {\n  amount: number;\n  currency: string;\n  customerId?: string;\n}\n\nexport interface ChatEventParams extends BaseEventParams {\n  channelId: string;\n  messageId?: string;\n}\n\nexport interface SupportEventParams extends BaseEventParams {\n  ticketId: string;\n  ticket: SupportTicket;\n}\n\nexport interface ContentEventParams extends BaseEventParams {\n  contentId: string;\n  content: RichTextContent;\n}\n\nexport interface AnalyticsEventParams extends BaseEventParams {\n  event: string;\n  properties?: Record<string, any>;\n}\n\nexport interface WebhookEventParams extends BaseEventParams {\n  source: string;\n  event: string;\n  payload: Record<string, any>;\n}\n\nexport interface FeedbackEventParams extends BaseEventParams {\n  feedbackId: string;\n  feedback: FeedbackEntry;\n}\n\nexport interface NewsletterEventParams extends BaseEventParams {\n  subscriberId?: string;\n  campaignId?: string;\n}\n\nexport interface UploadEventParams extends BaseEventParams {\n  fileId: string;\n  file: UploadedFile;\n}\n", "type": "registry:lib"}]}