{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "feedback-widget", "type": "registry:component", "title": "<PERSON><PERSON><PERSON> W<PERSON>t", "description": "User feedback collection widget with ratings, screenshots, categories, and admin dashboard for managing feedback", "dependencies": [], "registryDependencies": ["button", "input", "textarea", "label", "card", "badge", "select"], "files": [{"path": "lib/feedback-manager.ts", "type": "registry:lib"}, {"path": "types/feedback.ts", "type": "registry:type"}, {"path": "hooks/use-feedback.ts", "type": "registry:hook"}, {"path": "components/ui/feedback-widget.tsx", "type": "registry:component"}, {"path": "components/ui/feedback-form.tsx", "type": "registry:component"}, {"path": "actions/feedback-actions.ts", "type": "registry:lib"}, {"path": "app/api/feedback/route.ts", "type": "registry:lib", "target": "app/api/feedback/route.ts"}]}