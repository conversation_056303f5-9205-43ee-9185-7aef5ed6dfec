{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "subscriptions", "type": "registry:component", "title": "Stripe Subscriptions Module", "description": "Complete subscription management with pricing plans, subscription lifecycle, and recurring billing", "dependencies": ["stripe", "@stripe/stripe-js"], "registryDependencies": ["button", "card", "badge", "separator"], "files": [{"path": "lib/stripe.ts", "content": "/**\n * Supreme Toolkit - Stripe Configuration\n * \n * This file contains the Stripe client configuration and utilities\n * for the Supreme Toolkit payment module.\n */\n\nimport Stripe from 'stripe';\nimport { getModuleConfig } from '@/config';\n\n// ============================================================================\n// STRIPE CLIENT CONFIGURATION\n// ============================================================================\n\n/**\n * Server-side Stripe instance\n * Only use this on the server side (API routes, server actions)\n */\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2025-06-30.basil',\n  typescript: true,\n  maxNetworkRetries: 2, // Retry failed requests up to 2 times\n  timeout: 20 * 1000, // 20 second timeout\n  appInfo: {\n    name: 'Supreme Toolkit',\n    version: '1.0.0',\n    url: 'https://github.com/supreme-toolkit/supreme-toolkit',\n  },\n});\n\n/**\n * Get Stripe configuration from the toolkit config\n */\nexport function getStripeConfig() {\n  return getModuleConfig('stripe');\n}\n\n// ============================================================================\n// STRIPE UTILITIES\n// ============================================================================\n\n/**\n * Format price for display (converts cents to dollars)\n */\nexport function formatPrice(priceInCents: number, currency: string = 'usd'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n  }).format(priceInCents / 100);\n}\n\n/**\n * Convert dollars to cents for Stripe\n */\nexport function dollarsToCents(dollars: number): number {\n  return Math.round(dollars * 100);\n}\n\n/**\n * Convert cents to dollars\n */\nexport function centsToDollars(cents: number): number {\n  return cents / 100;\n}\n\n/**\n * Validate webhook signature\n */\nexport function validateWebhookSignature(\n  payload: string | Buffer,\n  signature: string,\n  webhookSecret: string\n): Stripe.Event {\n  try {\n    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);\n  } catch (error) {\n    throw new Error(`Webhook signature verification failed: ${error}`);\n  }\n}\n\n// ============================================================================\n// STRIPE TYPES\n// ============================================================================\n\nexport interface PaymentIntentData {\n  amount: number;\n  currency: string;\n  metadata?: Record<string, string>;\n  customerId?: string;\n  description?: string;\n}\n\nexport interface SubscriptionData {\n  customerId: string;\n  priceId: string;\n  metadata?: Record<string, string>;\n  trialPeriodDays?: number;\n}\n\nexport interface CustomerData {\n  email: string;\n  name?: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface PriceData {\n  productId: string;\n  unitAmount: number;\n  currency: string;\n  recurring?: {\n    interval: 'day' | 'week' | 'month' | 'year';\n    intervalCount?: number;\n  };\n}\n\n// ============================================================================\n// STRIPE HELPER FUNCTIONS\n// ============================================================================\n\n/**\n * Create a payment intent\n */\nexport async function createPaymentIntent(data: PaymentIntentData): Promise<Stripe.PaymentIntent> {\n  return await stripe.paymentIntents.create({\n    amount: data.amount,\n    currency: data.currency,\n    customer: data.customerId,\n    description: data.description,\n    metadata: data.metadata || {},\n    automatic_payment_methods: {\n      enabled: true,\n    },\n  });\n}\n\n/**\n * Create a customer\n */\nexport async function createCustomer(data: CustomerData): Promise<Stripe.Customer> {\n  return await stripe.customers.create({\n    email: data.email,\n    name: data.name,\n    metadata: data.metadata || {},\n  });\n}\n\n/**\n * Create a subscription\n */\nexport async function createSubscription(data: SubscriptionData): Promise<Stripe.Subscription> {\n  return await stripe.subscriptions.create({\n    customer: data.customerId,\n    items: [{ price: data.priceId }],\n    metadata: data.metadata || {},\n    trial_period_days: data.trialPeriodDays,\n  });\n}\n\n/**\n * Cancel a subscription\n */\nexport async function cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {\n  return await stripe.subscriptions.cancel(subscriptionId);\n}\n\n/**\n * Create a checkout session\n */\nexport async function createCheckoutSession(params: {\n  priceId: string;\n  customerId?: string;\n  successUrl: string;\n  cancelUrl: string;\n  mode?: 'payment' | 'subscription';\n  metadata?: Record<string, string>;\n}): Promise<Stripe.Checkout.Session> {\n  return await stripe.checkout.sessions.create({\n    mode: params.mode || 'payment',\n    customer: params.customerId,\n    line_items: [\n      {\n        price: params.priceId,\n        quantity: 1,\n      },\n    ],\n    success_url: params.successUrl,\n    cancel_url: params.cancelUrl,\n    metadata: params.metadata || {},\n  });\n}\n\n/**\n * Create a customer portal session\n */\nexport async function createCustomerPortalSession(\n  customerId: string,\n  returnUrl: string\n): Promise<Stripe.BillingPortal.Session> {\n  return await stripe.billingPortal.sessions.create({\n    customer: customerId,\n    return_url: returnUrl,\n  });\n}\n\n/**\n * Retrieve a customer by email\n */\nexport async function getCustomerByEmail(email: string): Promise<Stripe.Customer | null> {\n  const customers = await stripe.customers.list({\n    email: email,\n    limit: 1,\n  });\n  \n  return customers.data.length > 0 ? customers.data[0] : null;\n}\n\n/**\n * Get customer subscriptions\n */\nexport async function getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {\n  const subscriptions = await stripe.subscriptions.list({\n    customer: customerId,\n    status: 'all',\n  });\n  \n  return subscriptions.data;\n}\n\n/**\n * Get active subscription for customer\n */\nexport async function getActiveSubscription(customerId: string): Promise<Stripe.Subscription | null> {\n  const subscriptions = await stripe.subscriptions.list({\n    customer: customerId,\n    status: 'active',\n    limit: 1,\n  });\n  \n  return subscriptions.data.length > 0 ? subscriptions.data[0] : null;\n}\n\n// ============================================================================\n// STRIPE WEBHOOK EVENTS\n// ============================================================================\n\nexport const STRIPE_WEBHOOK_EVENTS = {\n  PAYMENT_INTENT_SUCCEEDED: 'payment_intent.succeeded',\n  PAYMENT_INTENT_PAYMENT_FAILED: 'payment_intent.payment_failed',\n  CUSTOMER_SUBSCRIPTION_CREATED: 'customer.subscription.created',\n  CUSTOMER_SUBSCRIPTION_UPDATED: 'customer.subscription.updated',\n  CUSTOMER_SUBSCRIPTION_DELETED: 'customer.subscription.deleted',\n  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',\n  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',\n  INVOICE_FINALIZED: 'invoice.finalized',\n} as const;\n\nexport type StripeWebhookEvent = typeof STRIPE_WEBHOOK_EVENTS[keyof typeof STRIPE_WEBHOOK_EVENTS];\n", "type": "registry:lib"}, {"path": "lib/pricing.ts", "content": "/**\n * Supreme Toolkit - Pricing Configuration System\n * \n * Centralized pricing configuration for managing plans, features,\n * and pricing tiers in the Supreme Toolkit.\n */\n\nimport { PricingCardProps } from '@/components/ui/pricing-card';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PricingPlan {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice?: number;\n  currency: string;\n  interval: 'month' | 'year' | 'week' | 'day' | 'one-time';\n  stripeProductId: string;\n  stripePriceId: string;\n  features: PricingFeature[];\n  popular?: boolean;\n  badge?: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface PricingFeature {\n  name: string;\n  included: boolean;\n  description?: string;\n  limit?: number | string;\n}\n\nexport interface PricingTier {\n  id: string;\n  name: string;\n  description: string;\n  plans: PricingPlan[];\n}\n\n// ============================================================================\n// DEFAULT PRICING PLANS\n// ============================================================================\n\nexport const defaultPricingPlans: PricingPlan[] = [\n  {\n    id: 'starter',\n    name: 'Starter',\n    description: 'Perfect for individuals getting started',\n    price: 9.99,\n    currency: 'USD',\n    interval: 'month',\n    stripeProductId: 'prod_starter',\n    stripePriceId: 'price_starter_monthly',\n    features: [\n      { name: '5 Projects', included: true },\n      { name: '10GB Storage', included: true },\n      { name: 'Email Support', included: true },\n      { name: 'Basic Analytics', included: true },\n      { name: 'API Access', included: false },\n      { name: 'Priority Support', included: false },\n      { name: 'Advanced Analytics', included: false },\n      { name: 'Custom Integrations', included: false },\n    ],\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    description: 'Best for growing businesses and teams',\n    price: 29.99,\n    originalPrice: 39.99,\n    currency: 'USD',\n    interval: 'month',\n    stripeProductId: 'prod_pro',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    popular: true,\n    features: [\n      { name: 'Unlimited Projects', included: true },\n      { name: '100GB Storage', included: true },\n      { name: 'Priority Email Support', included: true },\n      { name: 'Advanced Analytics', included: true },\n      { name: 'API Access', included: true },\n      { name: 'Team Collaboration', included: true },\n      { name: 'Priority Support', included: false },\n      { name: 'Custom Integrations', included: false },\n    ],\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    description: 'For large organizations with advanced needs',\n    price: 99.99,\n    currency: 'USD',\n    interval: 'month',\n    stripeProductId: 'prod_enterprise',\n    stripePriceId: 'price_enterprise_monthly',\n    badge: 'Most Advanced',\n    features: [\n      { name: 'Everything in Pro', included: true },\n      { name: 'Unlimited Storage', included: true },\n      { name: '24/7 Phone Support', included: true },\n      { name: 'Custom Integrations', included: true },\n      { name: 'Dedicated Account Manager', included: true },\n      { name: 'SLA Guarantee', included: true },\n      { name: 'On-premise Deployment', included: true },\n      { name: 'Custom Training', included: true },\n    ],\n  },\n];\n\n// ============================================================================\n// YEARLY PRICING PLANS (with discount)\n// ============================================================================\n\nexport const yearlyPricingPlans: PricingPlan[] = [\n  {\n    id: 'starter-yearly',\n    name: 'Starter',\n    description: 'Perfect for individuals getting started',\n    price: 99.99,\n    originalPrice: 119.88, // 9.99 * 12\n    currency: 'USD',\n    interval: 'year',\n    stripeProductId: 'prod_starter',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    features: defaultPricingPlans[0].features,\n  },\n  {\n    id: 'pro-yearly',\n    name: 'Pro',\n    description: 'Best for growing businesses and teams',\n    price: 299.99,\n    originalPrice: 359.88, // 29.99 * 12\n    currency: 'USD',\n    interval: 'year',\n    stripeProductId: 'prod_pro',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    popular: true,\n    features: defaultPricingPlans[1].features,\n  },\n  {\n    id: 'enterprise-yearly',\n    name: 'Enterprise',\n    description: 'For large organizations with advanced needs',\n    price: 999.99,\n    originalPrice: 1199.88, // 99.99 * 12\n    currency: 'USD',\n    interval: 'year',\n    stripeProductId: 'prod_enterprise',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    badge: 'Most Advanced',\n    features: defaultPricingPlans[2].features,\n  },\n];\n\n// ============================================================================\n// ONE-TIME PRICING PLANS\n// ============================================================================\n\nexport const oneTimePricingPlans: PricingPlan[] = [\n  {\n    id: 'basic-license',\n    name: 'Basic License',\n    description: 'One-time purchase for basic features',\n    price: 49.99,\n    currency: 'USD',\n    interval: 'one-time',\n    stripeProductId: 'prod_basic_license',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    features: [\n      { name: 'Lifetime Access', included: true },\n      { name: 'Basic Features', included: true },\n      { name: 'Email Support', included: true },\n      { name: 'Updates for 1 Year', included: true },\n      { name: 'Advanced Features', included: false },\n      { name: 'Priority Support', included: false },\n    ],\n  },\n  {\n    id: 'premium-license',\n    name: 'Premium License',\n    description: 'One-time purchase with all features',\n    price: 199.99,\n    currency: 'USD',\n    interval: 'one-time',\n    stripeProductId: 'prod_premium_license',\n    stripePriceId: 'price_1RhWNXSErasIwHrG0cjoxhBy',\n    popular: true,\n    features: [\n      { name: 'Lifetime Access', included: true },\n      { name: 'All Features', included: true },\n      { name: 'Priority Support', included: true },\n      { name: 'Lifetime Updates', included: true },\n      { name: 'Commercial License', included: true },\n      { name: 'Source Code Access', included: true },\n    ],\n  },\n];\n\n// ============================================================================\n// PRICING UTILITIES\n// ============================================================================\n\n/**\n * Get pricing plan by ID\n */\nexport function getPricingPlan(planId: string, interval: 'month' | 'year' | 'one-time' = 'month'): PricingPlan | null {\n  let plans: PricingPlan[];\n  \n  switch (interval) {\n    case 'year':\n      plans = yearlyPricingPlans;\n      break;\n    case 'one-time':\n      plans = oneTimePricingPlans;\n      break;\n    default:\n      plans = defaultPricingPlans;\n  }\n  \n  return plans.find(plan => plan.id === planId || plan.id === `${planId}-${interval}`) || null;\n}\n\n/**\n * Get all pricing plans for a specific interval\n */\nexport function getPricingPlans(interval: 'month' | 'year' | 'one-time' = 'month'): PricingPlan[] {\n  switch (interval) {\n    case 'year':\n      return yearlyPricingPlans;\n    case 'one-time':\n      return oneTimePricingPlans;\n    default:\n      return defaultPricingPlans;\n  }\n}\n\n/**\n * Convert pricing plan to PricingCard props\n */\nexport function planToPricingCardProps(plan: PricingPlan, options?: {\n  successUrl?: string;\n  cancelUrl?: string;\n  metadata?: Record<string, string>;\n}): PricingCardProps {\n  return {\n    title: plan.name,\n    description: plan.description,\n    price: plan.price,\n    originalPrice: plan.originalPrice,\n    currency: plan.currency,\n    interval: plan.interval,\n    priceId: plan.stripePriceId,\n    features: plan.features,\n    popular: plan.popular,\n    badge: plan.badge,\n    metadata: { ...plan.metadata, ...options?.metadata },\n    successUrl: options?.successUrl,\n    cancelUrl: options?.cancelUrl,\n  };\n}\n\n/**\n * Calculate discount percentage\n */\nexport function calculateDiscount(originalPrice: number, currentPrice: number): number {\n  if (originalPrice <= currentPrice) return 0;\n  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);\n}\n\n/**\n * Format price for display\n */\nexport function formatPrice(price: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: price % 1 === 0 ? 0 : 2,\n  }).format(price);\n}\n\n/**\n * Get pricing comparison data\n */\nexport function getPricingComparison(interval: 'month' | 'year' = 'month') {\n  const monthlyPlans = getPricingPlans('month');\n  const yearlyPlans = getPricingPlans('year');\n  \n  if (interval === 'month') {\n    return monthlyPlans.map(plan => planToPricingCardProps(plan));\n  }\n  \n  return yearlyPlans.map(plan => planToPricingCardProps(plan));\n}\n\n// ============================================================================\n// FEATURE COMPARISON\n// ============================================================================\n\n/**\n * Get all unique features across all plans\n */\nexport function getAllFeatures(): string[] {\n  const allFeatures = new Set<string>();\n  \n  [...defaultPricingPlans, ...yearlyPricingPlans, ...oneTimePricingPlans].forEach(plan => {\n    plan.features.forEach(feature => {\n      allFeatures.add(feature.name);\n    });\n  });\n  \n  return Array.from(allFeatures);\n}\n\n/**\n * Create feature comparison matrix\n */\nexport function createFeatureMatrix(interval: 'month' | 'year' | 'one-time' = 'month') {\n  const plans = getPricingPlans(interval);\n  const allFeatures = getAllFeatures();\n  \n  return {\n    features: allFeatures,\n    plans: plans.map(plan => ({\n      ...plan,\n      featureMap: allFeatures.reduce((map, featureName) => {\n        const feature = plan.features.find(f => f.name === featureName);\n        map[featureName] = feature ? feature.included : false;\n        return map;\n      }, {} as Record<string, boolean>),\n    })),\n  };\n}\n", "type": "registry:lib"}, {"path": "hooks/use-stripe.ts", "content": "\"use client\";\n\n/**\n * Supreme Toolkit - Stripe Hooks\n * \n * Custom React hooks for managing Stripe payments, subscriptions,\n * and customer data in the Supreme Toolkit.\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { getModuleConfig } from '@/config';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PaymentIntent {\n  id: string;\n  amount: number;\n  currency: string;\n  status: string;\n  clientSecret: string;\n}\n\nexport interface Subscription {\n  id: string;\n  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  cancelAtPeriodEnd: boolean;\n  trialEnd?: Date;\n  plan: {\n    id: string;\n    name: string;\n    amount: number;\n    currency: string;\n    interval: string;\n  };\n  customer: {\n    id: string;\n    email: string;\n  };\n}\n\nexport interface Customer {\n  id: string;\n  email: string;\n  name?: string;\n  subscriptions: Subscription[];\n}\n\nexport interface CheckoutSession {\n  id: string;\n  url: string;\n  mode: 'payment' | 'subscription';\n  status: string;\n}\n\n// ============================================================================\n// USE STRIPE CONFIG HOOK\n// ============================================================================\n\n/**\n * Hook to get Stripe configuration\n */\nexport function useStripeConfig() {\n  const [config, setConfig] = useState(() => {\n    try {\n      return getModuleConfig('stripe');\n    } catch {\n      return null;\n    }\n  });\n\n  return config;\n}\n\n// ============================================================================\n// USE CHECKOUT HOOK\n// ============================================================================\n\n/**\n * Hook for managing Stripe checkout sessions\n */\nexport function useCheckout() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createCheckoutSession = useCallback(async (params: {\n    priceId: string;\n    mode?: 'payment' | 'subscription';\n    successUrl?: string;\n    cancelUrl?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n  }) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/create-checkout-session', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          priceId: params.priceId,\n          mode: params.mode || 'payment',\n          successUrl: params.successUrl || `${window.location.origin}/payment/success`,\n          cancelUrl: params.cancelUrl || `${window.location.origin}/payment/cancel`,\n          customerId: params.customerId,\n          metadata: params.metadata,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(errorText || 'Failed to create checkout session');\n      }\n\n      const session: CheckoutSession = await response.json();\n      return session;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Checkout failed';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const redirectToCheckout = useCallback(async (params: {\n    priceId: string;\n    mode?: 'payment' | 'subscription';\n    successUrl?: string;\n    cancelUrl?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n  }) => {\n    try {\n      const session = await createCheckoutSession(params);\n      if (session.url) {\n        window.location.href = session.url;\n      }\n      return session;\n    } catch (err) {\n      throw err;\n    }\n  }, [createCheckoutSession]);\n\n  return {\n    createCheckoutSession,\n    redirectToCheckout,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USE SUBSCRIPTION HOOK\n// ============================================================================\n\n/**\n * Hook for managing user subscriptions\n */\nexport function useSubscription(customerId?: string) {\n  const [subscription, setSubscription] = useState<Subscription | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch subscription data\n  const fetchSubscription = useCallback(async () => {\n    if (!customerId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(`/api/stripe/subscription?customerId=${customerId}`);\n      \n      if (!response.ok) {\n        if (response.status === 404) {\n          setSubscription(null);\n          return;\n        }\n        throw new Error('Failed to fetch subscription');\n      }\n\n      const data = await response.json();\n      setSubscription(data);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch subscription';\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [customerId]);\n\n  // Cancel subscription\n  const cancelSubscription = useCallback(async (subscriptionId: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/cancel-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ subscriptionId }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to cancel subscription');\n      }\n\n      // Refresh subscription data\n      await fetchSubscription();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel subscription';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchSubscription]);\n\n  // Reactivate subscription\n  const reactivateSubscription = useCallback(async (subscriptionId: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/reactivate-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ subscriptionId }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to reactivate subscription');\n      }\n\n      // Refresh subscription data\n      await fetchSubscription();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to reactivate subscription';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchSubscription]);\n\n  // Load subscription on mount and when customerId changes\n  useEffect(() => {\n    fetchSubscription();\n  }, [fetchSubscription]);\n\n  return {\n    subscription,\n    loading,\n    error,\n    refetch: fetchSubscription,\n    cancelSubscription,\n    reactivateSubscription,\n  };\n}\n\n// ============================================================================\n// USE CUSTOMER PORTAL HOOK\n// ============================================================================\n\n/**\n * Hook for managing Stripe customer portal\n */\nexport function useCustomerPortal() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createPortalSession = useCallback(async (customerId: string, returnUrl?: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/customer-portal', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          customerId,\n          returnUrl: returnUrl || window.location.href,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create customer portal session');\n      }\n\n      const { url } = await response.json();\n      return url;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create portal session';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const redirectToPortal = useCallback(async (customerId: string, returnUrl?: string) => {\n    try {\n      const url = await createPortalSession(customerId, returnUrl);\n      if (url) {\n        window.location.href = url;\n      }\n    } catch (err) {\n      throw err;\n    }\n  }, [createPortalSession]);\n\n  return {\n    createPortalSession,\n    redirectToPortal,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USE PAYMENT INTENT HOOK\n// ============================================================================\n\n/**\n * Hook for managing payment intents\n */\nexport function usePaymentIntent() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createPaymentIntent = useCallback(async (params: {\n    amount: number;\n    currency?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n    description?: string;\n  }) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          amount: params.amount,\n          currency: params.currency || 'usd',\n          customerId: params.customerId,\n          metadata: params.metadata,\n          description: params.description,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create payment intent');\n      }\n\n      const paymentIntent: PaymentIntent = await response.json();\n      return paymentIntent;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create payment intent';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createPaymentIntent,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USAGE EXAMPLES\n// ============================================================================\n\n/*\n// Using checkout hook\nconst { redirectToCheckout, loading, error } = useCheckout();\n\nconst handlePurchase = async () => {\n  try {\n    await redirectToCheckout({\n      priceId: 'price_1234567890',\n      mode: 'payment',\n      successUrl: '/success',\n      cancelUrl: '/cancel',\n    });\n  } catch (err) {\n    console.error('Checkout failed:', err);\n  }\n};\n\n// Using subscription hook\nconst { subscription, loading, cancelSubscription } = useSubscription(customerId);\n\n// Using customer portal hook\nconst { redirectToPortal } = useCustomerPortal();\n\nconst handleManageSubscription = async () => {\n  try {\n    await redirectToPortal(customerId);\n  } catch (err) {\n    console.error('Portal failed:', err);\n  }\n};\n*/\n", "type": "registry:hook"}, {"path": "components/ui/pricing-card.tsx", "content": "\"use client\";\n\n/**\n * Supreme Toolkit - PricingCard Component\n * \n * A beautiful pricing card component that displays pricing plans\n * with integrated Stripe payment functionality.\n */\n\nimport React from 'react';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card';\nimport { Badge } from './badge';\nimport { PayButton } from './pay-button';\nimport { Check, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PricingFeature {\n  /** Feature name */\n  name: string;\n  /** Whether this feature is included */\n  included: boolean;\n  /** Optional description or tooltip */\n  description?: string;\n}\n\nexport interface PricingCardProps {\n  /** Plan name */\n  title: string;\n  /** Plan description */\n  description?: string;\n  /** Price amount (in dollars) */\n  price: number;\n  /** Price currency */\n  currency?: string;\n  /** Billing interval */\n  interval?: 'month' | 'year' | 'week' | 'day' | 'one-time';\n  /** Original price for showing discounts */\n  originalPrice?: number;\n  /** Stripe price ID */\n  priceId: string;\n  /** List of features */\n  features?: PricingFeature[];\n  /** Whether this is the popular/recommended plan */\n  popular?: boolean;\n  /** Custom badge text */\n  badge?: string;\n  /** Custom CSS classes */\n  className?: string;\n  /** Button text override */\n  buttonText?: string;\n  /** Button variant */\n  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  /** Whether the plan is disabled */\n  disabled?: boolean;\n  /** Custom metadata for payment */\n  metadata?: Record<string, string>;\n  /** Success URL after payment */\n  successUrl?: string;\n  /** Cancel URL if payment is cancelled */\n  cancelUrl?: string;\n  /** Callback when payment is initiated */\n  onPaymentStart?: () => void;\n  /** Callback when payment succeeds */\n  onPaymentSuccess?: (sessionId: string) => void;\n  /** Callback when payment fails */\n  onPaymentError?: (error: string) => void;\n}\n\n// ============================================================================\n// COMPONENT\n// ============================================================================\n\nexport function PricingCard({\n  title,\n  description,\n  price,\n  currency = 'USD',\n  interval = 'month',\n  originalPrice,\n  priceId,\n  features = [],\n  popular = false,\n  badge,\n  className,\n  buttonText,\n  buttonVariant,\n  disabled = false,\n  metadata,\n  successUrl,\n  cancelUrl,\n  onPaymentStart,\n  onPaymentSuccess,\n  onPaymentError,\n}: PricingCardProps) {\n  // Format price for display\n  const formatPrice = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n      minimumFractionDigits: amount % 1 === 0 ? 0 : 2,\n    }).format(amount);\n  };\n\n  // Get interval display text\n  const getIntervalText = () => {\n    switch (interval) {\n      case 'month':\n        return '/month';\n      case 'year':\n        return '/year';\n      case 'week':\n        return '/week';\n      case 'day':\n        return '/day';\n      case 'one-time':\n        return '';\n      default:\n        return `/${interval}`;\n    }\n  };\n\n  // Determine payment mode\n  const paymentMode = interval === 'one-time' ? 'payment' : 'subscription';\n\n  // Calculate discount percentage\n  const discountPercentage = originalPrice && originalPrice > price \n    ? Math.round(((originalPrice - price) / originalPrice) * 100)\n    : null;\n\n  return (\n    <Card \n      className={cn(\n        'relative flex flex-col',\n        popular && 'border-primary shadow-lg scale-105',\n        disabled && 'opacity-60',\n        className\n      )}\n    >\n      {/* Popular badge */}\n      {(popular || badge) && (\n        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n          <Badge variant={popular ? 'default' : 'secondary'}>\n            {badge || 'Most Popular'}\n          </Badge>\n        </div>\n      )}\n\n      <CardHeader className=\"text-center pb-4\">\n        <CardTitle className=\"text-2xl font-bold\">{title}</CardTitle>\n        {description && (\n          <CardDescription className=\"text-base\">{description}</CardDescription>\n        )}\n        \n        {/* Price display */}\n        <div className=\"mt-4\">\n          <div className=\"flex items-baseline justify-center gap-2\">\n            {originalPrice && originalPrice > price && (\n              <span className=\"text-lg text-muted-foreground line-through\">\n                {formatPrice(originalPrice)}\n              </span>\n            )}\n            <span className=\"text-4xl font-bold\">\n              {formatPrice(price)}\n            </span>\n            {interval !== 'one-time' && (\n              <span className=\"text-muted-foreground\">\n                {getIntervalText()}\n              </span>\n            )}\n          </div>\n          \n          {/* Discount badge */}\n          {discountPercentage && (\n            <div className=\"mt-2\">\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                Save {discountPercentage}%\n              </Badge>\n            </div>\n          )}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"flex-1\">\n        {/* Features list */}\n        {features.length > 0 && (\n          <ul className=\"space-y-3\">\n            {features.map((feature, index) => (\n              <li key={index} className=\"flex items-start gap-3\">\n                <div className=\"flex-shrink-0 mt-0.5\">\n                  {feature.included ? (\n                    <Check className=\"h-4 w-4 text-green-500\" />\n                  ) : (\n                    <X className=\"h-4 w-4 text-muted-foreground\" />\n                  )}\n                </div>\n                <div className=\"flex-1\">\n                  <span className={cn(\n                    'text-sm',\n                    !feature.included && 'text-muted-foreground line-through'\n                  )}>\n                    {feature.name}\n                  </span>\n                  {feature.description && (\n                    <p className=\"text-xs text-muted-foreground mt-1\">\n                      {feature.description}\n                    </p>\n                  )}\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </CardContent>\n\n      <CardFooter className=\"pt-4\">\n        <PayButton\n          priceId={priceId}\n          mode={paymentMode}\n          variant={buttonVariant || (popular ? 'default' : 'outline')}\n          size=\"lg\"\n          className=\"w-full\"\n          disabled={disabled}\n          metadata={metadata}\n          successUrl={successUrl}\n          cancelUrl={cancelUrl}\n          onPaymentStart={onPaymentStart}\n          onPaymentSuccess={onPaymentSuccess}\n          onPaymentError={onPaymentError}\n        >\n          {buttonText || (paymentMode === 'subscription' ? 'Subscribe' : 'Purchase')}\n        </PayButton>\n      </CardFooter>\n    </Card>\n  );\n}\n\n// ============================================================================\n// PRICING GRID COMPONENT\n// ============================================================================\n\nexport interface PricingGridProps {\n  /** Array of pricing plans */\n  plans: PricingCardProps[];\n  /** Custom CSS classes */\n  className?: string;\n  /** Number of columns (responsive) */\n  columns?: 1 | 2 | 3 | 4;\n}\n\nexport function PricingGrid({ \n  plans, \n  className,\n  columns = 3 \n}: PricingGridProps) {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n  };\n\n  return (\n    <div className={cn(\n      'grid gap-8',\n      gridCols[columns],\n      className\n    )}>\n      {plans.map((plan, index) => (\n        <PricingCard key={index} {...plan} />\n      ))}\n    </div>\n  );\n}\n\n// ============================================================================\n// USAGE EXAMPLES\n// ============================================================================\n\n/*\n// Basic usage\n<PricingCard\n  title=\"Pro Plan\"\n  description=\"Perfect for growing businesses\"\n  price={29.99}\n  interval=\"month\"\n  priceId=\"price_1234567890\"\n  features={[\n    { name: \"Unlimited projects\", included: true },\n    { name: \"24/7 support\", included: true },\n    { name: \"Advanced analytics\", included: false },\n  ]}\n  popular={true}\n/>\n\n// Pricing grid\n<PricingGrid\n  columns={3}\n  plans={[\n    {\n      title: \"Starter\",\n      price: 9.99,\n      priceId: \"price_starter\",\n      features: [\n        { name: \"5 projects\", included: true },\n        { name: \"Email support\", included: true },\n      ]\n    },\n    {\n      title: \"Pro\",\n      price: 29.99,\n      originalPrice: 39.99,\n      priceId: \"price_pro\",\n      popular: true,\n      features: [\n        { name: \"Unlimited projects\", included: true },\n        { name: \"24/7 support\", included: true },\n      ]\n    },\n    {\n      title: \"Enterprise\",\n      price: 99.99,\n      priceId: \"price_enterprise\",\n      features: [\n        { name: \"Everything in Pro\", included: true },\n        { name: \"Custom integrations\", included: true },\n      ]\n    }\n  ]}\n/>\n*/\n", "type": "registry:component"}, {"path": "components/ui/subscription-manager.tsx", "content": "\"use client\";\n\n/**\n * Supreme Toolkit - Subscription Manager Component\n * \n * A component for managing user subscriptions, including viewing current\n * subscription status, upgrading/downgrading plans, and accessing the\n * customer portal.\n */\n\nimport React from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';\nimport { Button } from './button';\nimport { Badge } from './badge';\nimport { Separator } from './separator';\nimport {\n  Calendar,\n  ExternalLink,\n  Loader2,\n  Settings,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface SubscriptionData {\n  id: string;\n  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  cancelAtPeriodEnd: boolean;\n  trialEnd?: Date;\n  plan: {\n    id: string;\n    name: string;\n    amount: number;\n    currency: string;\n    interval: string;\n  };\n  customer: {\n    id: string;\n    email: string;\n  };\n}\n\nexport interface SubscriptionManagerProps {\n  /** Current subscription data */\n  subscription?: SubscriptionData;\n  /** Loading state */\n  loading?: boolean;\n  /** Custom CSS classes */\n  className?: string;\n  /** Callback when customer portal is accessed */\n  onCustomerPortal?: () => void;\n  /** Callback when subscription is cancelled */\n  onCancelSubscription?: () => void;\n  /** Callback when subscription is reactivated */\n  onReactivateSubscription?: () => void;\n}\n\n// ============================================================================\n// COMPONENT\n// ============================================================================\n\nexport function SubscriptionManager({\n  subscription,\n  loading = false,\n  className,\n  onCustomerPortal,\n  onCancelSubscription,\n  onReactivateSubscription,\n}: SubscriptionManagerProps) {\n  const [isLoading, setIsLoading] = React.useState(false);\n\n  // Format price for display\n  const formatPrice = (amount: number, currency: string) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency.toUpperCase(),\n    }).format(amount / 100);\n  };\n\n  // Format date for display\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    }).format(date);\n  };\n\n  // Get status badge variant and text\n  const getStatusInfo = (status: SubscriptionData['status']) => {\n    switch (status) {\n      case 'active':\n        return { variant: 'default' as const, text: 'Active', icon: CheckCircle };\n      case 'trialing':\n        return { variant: 'secondary' as const, text: 'Trial', icon: Calendar };\n      case 'canceled':\n        return { variant: 'destructive' as const, text: 'Canceled', icon: AlertTriangle };\n      case 'past_due':\n        return { variant: 'destructive' as const, text: 'Past Due', icon: AlertTriangle };\n      case 'incomplete':\n        return { variant: 'secondary' as const, text: 'Incomplete', icon: AlertTriangle };\n      default:\n        return { variant: 'secondary' as const, text: status, icon: AlertTriangle };\n    }\n  };\n\n  // Handle customer portal access\n  const handleCustomerPortal = async () => {\n    if (!subscription) return;\n    \n    try {\n      setIsLoading(true);\n      \n      const response = await fetch('/api/stripe/customer-portal', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          customerId: subscription.customer.id,\n          returnUrl: window.location.href,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create customer portal session');\n      }\n\n      const { url } = await response.json();\n      \n      if (url) {\n        window.location.href = url;\n        onCustomerPortal?.();\n      }\n    } catch (error) {\n      console.error('Customer portal error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle subscription cancellation\n  const handleCancelSubscription = async () => {\n    if (!subscription) return;\n    \n    try {\n      setIsLoading(true);\n      \n      const response = await fetch('/api/stripe/cancel-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          subscriptionId: subscription.id,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to cancel subscription');\n      }\n\n      onCancelSubscription?.();\n    } catch (error) {\n      console.error('Cancel subscription error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle subscription reactivation\n  const handleReactivateSubscription = async () => {\n    if (!subscription) return;\n    \n    try {\n      setIsLoading(true);\n      \n      const response = await fetch('/api/stripe/reactivate-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          subscriptionId: subscription.id,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to reactivate subscription');\n      }\n\n      onReactivateSubscription?.();\n    } catch (error) {\n      console.error('Reactivate subscription error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span className=\"ml-2\">Loading subscription...</span>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (!subscription) {\n    return (\n      <Card className={className}>\n        <CardHeader>\n          <CardTitle>No Active Subscription</CardTitle>\n          <CardDescription>\n            You don't have an active subscription. Choose a plan to get started.\n          </CardDescription>\n        </CardHeader>\n      </Card>\n    );\n  }\n\n  const statusInfo = getStatusInfo(subscription.status);\n  const StatusIcon = statusInfo.icon;\n  const isTrialing = subscription.status === 'trialing';\n  const isCanceled = subscription.cancelAtPeriodEnd;\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <StatusIcon className=\"h-5 w-5\" />\n              {subscription.plan.name}\n            </CardTitle>\n            <CardDescription>\n              {formatPrice(subscription.plan.amount, subscription.plan.currency)} per {subscription.plan.interval}\n            </CardDescription>\n          </div>\n          <Badge variant={statusInfo.variant}>\n            {statusInfo.text}\n          </Badge>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* Subscription details */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-muted-foreground\">Customer Email</span>\n            <span>{subscription.customer.email}</span>\n          </div>\n          \n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-muted-foreground\">Current Period</span>\n            <span>\n              {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}\n            </span>\n          </div>\n\n          {isTrialing && subscription.trialEnd && (\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-muted-foreground\">Trial Ends</span>\n              <span>{formatDate(subscription.trialEnd)}</span>\n            </div>\n          )}\n\n          {isCanceled && (\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-muted-foreground\">Cancels On</span>\n              <span className=\"text-destructive\">\n                {formatDate(subscription.currentPeriodEnd)}\n              </span>\n            </div>\n          )}\n        </div>\n\n        <Separator />\n\n        {/* Action buttons */}\n        <div className=\"flex flex-col gap-2\">\n          <Button\n            onClick={handleCustomerPortal}\n            disabled={isLoading}\n            variant=\"outline\"\n            className=\"w-full\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n            ) : (\n              <Settings className=\"mr-2 h-4 w-4\" />\n            )}\n            Manage Subscription\n            <ExternalLink className=\"ml-2 h-4 w-4\" />\n          </Button>\n\n          {isCanceled ? (\n            <Button\n              onClick={handleReactivateSubscription}\n              disabled={isLoading}\n              variant=\"default\"\n              className=\"w-full\"\n            >\n              Reactivate Subscription\n            </Button>\n          ) : (\n            subscription.status === 'active' && (\n              <Button\n                onClick={handleCancelSubscription}\n                disabled={isLoading}\n                variant=\"destructive\"\n                className=\"w-full\"\n              >\n                Cancel Subscription\n              </Button>\n            )\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\n// ============================================================================\n// USAGE EXAMPLES\n// ============================================================================\n\n/*\n// Basic usage\n<SubscriptionManager\n  subscription={{\n    id: \"sub_1234567890\",\n    status: \"active\",\n    currentPeriodStart: new Date(\"2024-01-01\"),\n    currentPeriodEnd: new Date(\"2024-02-01\"),\n    cancelAtPeriodEnd: false,\n    plan: {\n      id: \"price_1234567890\",\n      name: \"Pro Plan\",\n      amount: 2999,\n      currency: \"usd\",\n      interval: \"month\"\n    },\n    customer: {\n      id: \"cus_1234567890\",\n      email: \"<EMAIL>\"\n    }\n  }}\n  onCustomerPortal={() => {\n    console.log('Customer portal accessed');\n  }}\n  onCancelSubscription={() => {\n    console.log('Subscription cancelled');\n    // Refresh subscription data\n  }}\n/>\n*/\n", "type": "registry:component"}, {"path": "actions/stripe-actions.ts", "content": "\"use server\";\n\n/**\n * Supreme Toolkit - Stripe Server Actions\n * \n * Server actions for handling Stripe payment events and operations.\n * These actions are triggered by webhooks and user interactions.\n */\n\nimport { stripe } from '@/lib/stripe';\nimport Stripe from 'stripe';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PaymentCompleteData {\n  paymentIntentId: string;\n  customerId: string;\n  amount: number;\n  currency: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface SubscriptionCreatedData {\n  subscriptionId: string;\n  customerId: string;\n  priceId: string;\n  status: string;\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  metadata?: Record<string, string>;\n}\n\nexport interface SubscriptionCancelledData {\n  subscriptionId: string;\n  customerId: string;\n  canceledAt: Date;\n  cancelAtPeriodEnd: boolean;\n  metadata?: Record<string, string>;\n}\n\nexport interface PaymentFailedData {\n  paymentIntentId: string;\n  customerId?: string;\n  amount: number;\n  currency: string;\n  failureCode?: string;\n  failureMessage?: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface InvoiceGeneratedData {\n  invoiceId: string;\n  customerId: string;\n  subscriptionId?: string;\n  amount: number;\n  currency: string;\n  status: string;\n  dueDate?: Date;\n  metadata?: Record<string, string>;\n}\n\n// ============================================================================\n// PAYMENT EVENTS\n// ============================================================================\n\n/**\n * Called when a payment is successfully completed\n * Customize this function to handle successful payments in your app\n */\nexport async function onPaymentComplete(data: PaymentCompleteData) {\n  try {\n    console.log('Payment completed:', data);\n\n    // Example: Update user's account status\n    // await updateUserPremiumStatus(data.customerId, true);\n\n    // Example: Send confirmation email\n    // await sendPaymentConfirmationEmail(data.customerId, data.amount);\n\n    // Example: Log the payment for analytics\n    // await logPaymentEvent('payment_completed', data);\n\n    // Example: Grant access to paid features\n    // await grantPremiumAccess(data.customerId);\n\n    // You can customize this function based on your app's needs\n    return {\n      success: true,\n      message: 'Payment processed successfully',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onPaymentComplete:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Called when a payment fails\n * Customize this function to handle failed payments in your app\n */\nexport async function onPaymentFailed(data: PaymentFailedData) {\n  try {\n    console.log('Payment failed:', data);\n\n    // Example: Send failure notification email\n    // await sendPaymentFailureEmail(data.customerId, data.failureMessage);\n\n    // Example: Log the failure for analytics\n    // await logPaymentEvent('payment_failed', data);\n\n    // Example: Retry payment logic\n    // await schedulePaymentRetry(data.paymentIntentId);\n\n    return {\n      success: true,\n      message: 'Payment failure handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onPaymentFailed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// SUBSCRIPTION EVENTS\n// ============================================================================\n\n/**\n * Called when a new subscription is created\n * Customize this function to handle new subscriptions in your app\n */\nexport async function onSubscriptionCreated(data: SubscriptionCreatedData) {\n  try {\n    console.log('Subscription created:', data);\n\n    // Example: Update user's subscription status\n    // await updateUserSubscription(data.customerId, {\n    //   subscriptionId: data.subscriptionId,\n    //   status: 'active',\n    //   plan: data.priceId,\n    //   currentPeriodEnd: data.currentPeriodEnd,\n    // });\n\n    // Example: Send welcome email\n    // await sendSubscriptionWelcomeEmail(data.customerId);\n\n    // Example: Grant subscription features\n    // await grantSubscriptionAccess(data.customerId, data.priceId);\n\n    // Example: Log subscription event\n    // await logSubscriptionEvent('subscription_created', data);\n\n    return {\n      success: true,\n      message: 'Subscription created successfully',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onSubscriptionCreated:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Called when a subscription is cancelled\n * Customize this function to handle subscription cancellations in your app\n */\nexport async function onSubscriptionCancelled(data: SubscriptionCancelledData) {\n  try {\n    console.log('Subscription cancelled:', data);\n\n    // Example: Update user's subscription status\n    // await updateUserSubscription(data.customerId, {\n    //   subscriptionId: data.subscriptionId,\n    //   status: 'cancelled',\n    //   canceledAt: data.canceledAt,\n    //   cancelAtPeriodEnd: data.cancelAtPeriodEnd,\n    // });\n\n    // Example: Send cancellation confirmation email\n    // await sendSubscriptionCancellationEmail(data.customerId);\n\n    // Example: Schedule access removal (if cancel at period end)\n    // if (data.cancelAtPeriodEnd) {\n    //   await scheduleAccessRemoval(data.customerId, data.canceledAt);\n    // } else {\n    //   await revokeSubscriptionAccess(data.customerId);\n    // }\n\n    // Example: Log cancellation event\n    // await logSubscriptionEvent('subscription_cancelled', data);\n\n    return {\n      success: true,\n      message: 'Subscription cancellation handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onSubscriptionCancelled:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// INVOICE EVENTS\n// ============================================================================\n\n/**\n * Called when an invoice is generated\n * Customize this function to handle invoice generation in your app\n */\nexport async function onInvoiceGenerated(data: InvoiceGeneratedData) {\n  try {\n    console.log('Invoice generated:', data);\n\n    // Example: Send invoice email to customer\n    // await sendInvoiceEmail(data.customerId, data.invoiceId);\n\n    // Example: Update billing records\n    // await updateBillingRecord(data.customerId, {\n    //   invoiceId: data.invoiceId,\n    //   amount: data.amount,\n    //   status: data.status,\n    //   dueDate: data.dueDate,\n    // });\n\n    // Example: Log invoice event\n    // await logInvoiceEvent('invoice_generated', data);\n\n    return {\n      success: true,\n      message: 'Invoice generation handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onInvoiceGenerated:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// UTILITY FUNCTIONS\n// ============================================================================\n\n/**\n * Create a customer in Stripe\n */\nexport async function createStripeCustomer(email: string, name?: string, metadata?: Record<string, string>) {\n  try {\n    const customer = await stripe.customers.create({\n      email,\n      name,\n      metadata: metadata || {},\n    });\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error creating Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Update a customer in Stripe\n */\nexport async function updateStripeCustomer(\n  customerId: string, \n  updates: { email?: string; name?: string; metadata?: Record<string, string> }\n) {\n  try {\n    const customer = await stripe.customers.update(customerId, updates);\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error updating Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Get customer by email\n */\nexport async function getStripeCustomerByEmail(email: string) {\n  try {\n    const customers = await stripe.customers.list({\n      email: email,\n      limit: 1,\n    });\n\n    const customer = customers.data.length > 0 ? customers.data[0] : null;\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error getting Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Cancel a subscription\n */\nexport async function cancelStripeSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true) {\n  try {\n    let subscription: Stripe.Subscription;\n\n    if (cancelAtPeriodEnd) {\n      subscription = await stripe.subscriptions.update(subscriptionId, {\n        cancel_at_period_end: true,\n      });\n    } else {\n      subscription = await stripe.subscriptions.cancel(subscriptionId);\n    }\n\n    return {\n      success: true,\n      subscription,\n    };\n  } catch (error) {\n    console.error('Error cancelling Stripe subscription:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Reactivate a subscription\n */\nexport async function reactivateStripeSubscription(subscriptionId: string) {\n  try {\n    const subscription = await stripe.subscriptions.update(subscriptionId, {\n      cancel_at_period_end: false,\n    });\n\n    return {\n      success: true,\n      subscription,\n    };\n  } catch (error) {\n    console.error('Error reactivating Stripe subscription:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// WEBHOOK PROCESSING\n// ============================================================================\n\n/**\n * Process Stripe webhook events\n * This function routes webhook events to the appropriate handlers\n */\nexport async function processStripeWebhook(event: Stripe.Event) {\n  try {\n    switch (event.type) {\n      case 'payment_intent.succeeded': {\n        const paymentIntent = event.data.object as Stripe.PaymentIntent;\n        await onPaymentComplete({\n          paymentIntentId: paymentIntent.id,\n          customerId: paymentIntent.customer as string,\n          amount: paymentIntent.amount,\n          currency: paymentIntent.currency,\n          metadata: paymentIntent.metadata,\n        });\n        break;\n      }\n\n      case 'payment_intent.payment_failed': {\n        const paymentIntent = event.data.object as Stripe.PaymentIntent;\n        await onPaymentFailed({\n          paymentIntentId: paymentIntent.id,\n          customerId: paymentIntent.customer as string,\n          amount: paymentIntent.amount,\n          currency: paymentIntent.currency,\n          failureCode: paymentIntent.last_payment_error?.code,\n          failureMessage: paymentIntent.last_payment_error?.message,\n          metadata: paymentIntent.metadata,\n        });\n        break;\n      }\n\n      case 'customer.subscription.created': {\n        const subscription = event.data.object as Stripe.Subscription;\n        await onSubscriptionCreated({\n          subscriptionId: subscription.id,\n          customerId: subscription.customer as string,\n          priceId: subscription.items.data[0].price.id,\n          status: subscription.status,\n          currentPeriodStart: new Date((subscription as any).current_period_start * 1000),\n          currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),\n          metadata: subscription.metadata || {},\n        });\n        break;\n      }\n\n      case 'customer.subscription.deleted': {\n        const subscription = event.data.object as Stripe.Subscription;\n        await onSubscriptionCancelled({\n          subscriptionId: subscription.id,\n          customerId: subscription.customer as string,\n          canceledAt: new Date((subscription as any).canceled_at! * 1000),\n          cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,\n          metadata: subscription.metadata || {},\n        });\n        break;\n      }\n\n      case 'invoice.finalized': {\n        const invoice = event.data.object as Stripe.Invoice;\n        await onInvoiceGenerated({\n          invoiceId: invoice.id!,\n          customerId: invoice.customer as string,\n          subscriptionId: (invoice as any).subscription as string || undefined,\n          amount: invoice.amount_due,\n          currency: invoice.currency,\n          status: invoice.status!,\n          dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : undefined,\n          metadata: invoice.metadata || {},\n        });\n        break;\n      }\n\n      default:\n        console.log(`Unhandled event type: ${event.type}`);\n    }\n\n    return { success: true };\n  } catch (error) {\n    console.error('Error processing webhook:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n", "type": "registry:lib"}, {"path": "app/api/stripe/create-checkout-session/route.ts", "content": "/**\n * Supreme Toolkit - Create Checkout Session API Route\n * \n * Creates a Stripe checkout session for payments and subscriptions.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { createCheckoutSession } from '@/lib/stripe';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface CreateCheckoutSessionRequest {\n  priceId: string;\n  mode?: 'payment' | 'subscription';\n  successUrl: string;\n  cancelUrl: string;\n  customerId?: string;\n  metadata?: Record<string, string>;\n}\n\n// ============================================================================\n// API HANDLER\n// ============================================================================\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Parse request body\n    const body: CreateCheckoutSessionRequest = await request.json();\n    \n    // Validate required fields\n    if (!body.priceId) {\n      return NextResponse.json(\n        { error: 'Price ID is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!body.successUrl) {\n      return NextResponse.json(\n        { error: 'Success URL is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!body.cancelUrl) {\n      return NextResponse.json(\n        { error: 'Cancel URL is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create checkout session\n    const session = await createCheckoutSession({\n      priceId: body.priceId,\n      customerId: body.customerId,\n      successUrl: body.successUrl,\n      cancelUrl: body.cancelUrl,\n      mode: body.mode || 'payment',\n      metadata: body.metadata,\n    });\n\n    // Return session data\n    return NextResponse.json({\n      sessionId: session.id,\n      url: session.url,\n      mode: session.mode,\n    });\n\n  } catch (error) {\n    console.error('Create checkout session error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to create checkout session' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// SUPPORTED METHODS\n// ============================================================================\n\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n", "type": "registry:lib", "target": "app/api/stripe/create-checkout-session/route.ts"}, {"path": "app/api/stripe/subscription/route.ts", "content": "/**\n * Supreme Toolkit - Subscription API Route\n * \n * Handles subscription-related operations like fetching, canceling, and reactivating subscriptions.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { getActiveSubscription } from '@/lib/stripe';\nimport { cancelStripeSubscription, reactivateStripeSubscription } from '@/actions/stripe-actions';\n\n// ============================================================================\n// GET SUBSCRIPTION\n// ============================================================================\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const customerId = searchParams.get('customerId');\n    \n    if (!customerId) {\n      return NextResponse.json(\n        { error: 'Customer ID is required' },\n        { status: 400 }\n      );\n    }\n\n    // Get active subscription for customer\n    const subscription = await getActiveSubscription(customerId);\n    \n    if (!subscription) {\n      return NextResponse.json(\n        { error: 'No active subscription found' },\n        { status: 404 }\n      );\n    }\n\n    // Format subscription data\n    const subscriptionData = {\n      id: subscription.id,\n      status: subscription.status,\n      currentPeriodStart: new Date((subscription as any).current_period_start * 1000),\n      currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),\n      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,\n      trialEnd: (subscription as any).trial_end ? new Date((subscription as any).trial_end * 1000) : undefined,\n      plan: {\n        id: subscription.items.data[0].price.id,\n        name: subscription.items.data[0].price.nickname || 'Subscription',\n        amount: subscription.items.data[0].price.unit_amount || 0,\n        currency: subscription.items.data[0].price.currency,\n        interval: subscription.items.data[0].price.recurring?.interval || 'month',\n      },\n      customer: {\n        id: subscription.customer as string,\n        email: typeof subscription.customer === 'object' && 'email' in subscription.customer ? subscription.customer.email || '' : '',\n      },\n    };\n\n    return NextResponse.json(subscriptionData);\n\n  } catch (error) {\n    console.error('Get subscription error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to fetch subscription' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// CANCEL SUBSCRIPTION\n// ============================================================================\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { subscriptionId, cancelAtPeriodEnd = true } = body;\n    \n    if (!subscriptionId) {\n      return NextResponse.json(\n        { error: 'Subscription ID is required' },\n        { status: 400 }\n      );\n    }\n\n    // Cancel the subscription\n    const result = await cancelStripeSubscription(subscriptionId, cancelAtPeriodEnd);\n    \n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      subscription: result.subscription,\n    });\n\n  } catch (error) {\n    console.error('Cancel subscription error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to cancel subscription' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// UPDATE SUBSCRIPTION (for reactivation)\n// ============================================================================\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { subscriptionId, action } = body;\n    \n    if (!subscriptionId) {\n      return NextResponse.json(\n        { error: 'Subscription ID is required' },\n        { status: 400 }\n      );\n    }\n\n    if (action === 'reactivate') {\n      // Reactivate the subscription\n      const result = await reactivateStripeSubscription(subscriptionId);\n      \n      if (!result.success) {\n        return NextResponse.json(\n          { error: result.error },\n          { status: 500 }\n        );\n      }\n\n      return NextResponse.json({\n        success: true,\n        subscription: result.subscription,\n      });\n    }\n\n    return NextResponse.json(\n      { error: 'Invalid action' },\n      { status: 400 }\n    );\n\n  } catch (error) {\n    console.error('Update subscription error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to update subscription' \n      },\n      { status: 500 }\n    );\n  }\n}\n", "type": "registry:lib", "target": "app/api/stripe/subscription/route.ts"}, {"path": "app/api/stripe/cancel-subscription/route.ts", "content": "/**\n * Supreme Toolkit - Cancel Subscription API Route\n * \n * Handles subscription cancellation requests.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { cancelStripeSubscription } from '@/actions/stripe-actions';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface CancelSubscriptionRequest {\n  subscriptionId: string;\n  cancelAtPeriodEnd?: boolean;\n}\n\n// ============================================================================\n// API HANDLER\n// ============================================================================\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Parse request body\n    const body: CancelSubscriptionRequest = await request.json();\n    \n    // Validate required fields\n    if (!body.subscriptionId) {\n      return NextResponse.json(\n        { error: 'Subscription ID is required' },\n        { status: 400 }\n      );\n    }\n\n    // Cancel the subscription\n    const result = await cancelStripeSubscription(\n      body.subscriptionId, \n      body.cancelAtPeriodEnd ?? true\n    );\n    \n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      subscription: result.subscription,\n      message: body.cancelAtPeriodEnd \n        ? 'Subscription will be cancelled at the end of the current period'\n        : 'Subscription cancelled immediately',\n    });\n\n  } catch (error) {\n    console.error('Cancel subscription error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to cancel subscription' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// SUPPORTED METHODS\n// ============================================================================\n\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n", "type": "registry:lib", "target": "app/api/stripe/cancel-subscription/route.ts"}, {"path": "app/api/stripe/reactivate-subscription/route.ts", "content": "/**\n * Supreme Toolkit - Reactivate Subscription API Route\n * \n * Handles subscription reactivation requests.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { reactivateStripeSubscription } from '@/actions/stripe-actions';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface ReactivateSubscriptionRequest {\n  subscriptionId: string;\n}\n\n// ============================================================================\n// API HANDLER\n// ============================================================================\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Parse request body\n    const body: ReactivateSubscriptionRequest = await request.json();\n    \n    // Validate required fields\n    if (!body.subscriptionId) {\n      return NextResponse.json(\n        { error: 'Subscription ID is required' },\n        { status: 400 }\n      );\n    }\n\n    // Reactivate the subscription\n    const result = await reactivateStripeSubscription(body.subscriptionId);\n    \n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      subscription: result.subscription,\n      message: 'Subscription reactivated successfully',\n    });\n\n  } catch (error) {\n    console.error('Reactivate subscription error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to reactivate subscription' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// SUPPORTED METHODS\n// ============================================================================\n\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n", "type": "registry:lib", "target": "app/api/stripe/reactivate-subscription/route.ts"}, {"path": "app/api/stripe/webhooks/route.ts", "content": "/**\n * Supreme Toolkit - Stripe Webhooks API Route\n * \n * Handles incoming Stripe webhook events securely.\n * This endpoint processes payment and subscription events from Stripe.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { headers } from 'next/headers';\nimport { validateWebhookSignature } from '@/lib/stripe';\nimport { processStripeWebhook } from '@/actions/stripe-actions';\nimport { getModuleConfig } from '@/config';\n\n// ============================================================================\n// WEBHOOK HANDLER\n// ============================================================================\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Get the raw body\n    const body = await request.text();\n    \n    // Get the signature from headers\n    const headersList = await headers();\n    const signature = headersList.get('stripe-signature');\n    \n    if (!signature) {\n      console.error('Missing Stripe signature');\n      return NextResponse.json(\n        { error: 'Missing Stripe signature' },\n        { status: 400 }\n      );\n    }\n\n    // Get webhook secret from config\n    const stripeConfig = getModuleConfig('stripe');\n    const webhookSecret = stripeConfig?.webhookSecret;\n    \n    if (!webhookSecret) {\n      console.error('Missing webhook secret in configuration');\n      return NextResponse.json(\n        { error: 'Webhook secret not configured' },\n        { status: 500 }\n      );\n    }\n\n    // Validate the webhook signature\n    let event;\n    try {\n      event = validateWebhookSignature(body, signature, webhookSecret);\n    } catch (error) {\n      console.error('Webhook signature verification failed:', error);\n      return NextResponse.json(\n        { error: 'Invalid signature' },\n        { status: 400 }\n      );\n    }\n\n    // Log the event for debugging\n    console.log(`Received Stripe webhook: ${event.type}`);\n\n    // Process the webhook event\n    const result = await processStripeWebhook(event);\n    \n    if (!result.success) {\n      console.error('Webhook processing failed:', result.error);\n      return NextResponse.json(\n        { error: 'Webhook processing failed' },\n        { status: 500 }\n      );\n    }\n\n    // Return success response\n    return NextResponse.json({ received: true });\n\n  } catch (error) {\n    console.error('Webhook error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// SUPPORTED METHODS\n// ============================================================================\n\n// Only allow POST requests\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function PUT() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function DELETE() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n", "type": "registry:lib", "target": "app/api/stripe/webhooks/route.ts"}]}