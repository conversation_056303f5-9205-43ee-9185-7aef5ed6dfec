{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "chat-realtime", "type": "registry:component", "title": "Real-time <PERSON><PERSON>", "description": "Complete real-time chat system with Supabase backend, message handling, presence indicators, and room management", "dependencies": ["@supabase/supabase-js", "date-fns"], "registryDependencies": ["button", "input", "textarea", "card", "avatar", "badge", "scroll-area", "separator", "dropdown-menu"], "files": [{"path": "lib/supabase-chat.ts", "type": "registry:lib"}, {"path": "types/chat.ts", "type": "registry:type"}, {"path": "hooks/use-chat.ts", "type": "registry:hook"}, {"path": "hooks/use-rooms.ts", "type": "registry:hook"}, {"path": "components/ui/chat-room.tsx", "type": "registry:component"}, {"path": "components/ui/chat-message.tsx", "type": "registry:component"}, {"path": "components/ui/chat-input.tsx", "type": "registry:component"}, {"path": "components/ui/chat-user-list.tsx", "type": "registry:component"}, {"path": "actions/chat-actions.ts", "type": "registry:lib"}, {"path": "app/api/chat/route.ts", "type": "registry:lib", "target": "app/api/chat/route.ts"}]}