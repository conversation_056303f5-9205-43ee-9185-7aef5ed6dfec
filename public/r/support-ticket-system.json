{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "support-ticket-system", "type": "registry:component", "title": "Support Ticket System", "description": "Complete support ticket system with ticket creation, status management, comments, categories, and admin dashboard", "dependencies": ["date-fns"], "registryDependencies": ["button", "input", "textarea", "label", "card", "badge", "scroll-area", "select"], "files": [{"path": "lib/ticket-manager.ts", "type": "registry:lib"}, {"path": "types/tickets.ts", "type": "registry:type"}, {"path": "hooks/use-tickets.ts", "type": "registry:hook"}, {"path": "hooks/use-ticket-comments.ts", "type": "registry:hook"}, {"path": "components/ui/ticket-list.tsx", "type": "registry:component"}, {"path": "components/ui/ticket-form.tsx", "type": "registry:component"}, {"path": "actions/ticket-actions.ts", "type": "registry:lib"}, {"path": "app/api/tickets/route.ts", "type": "registry:lib", "target": "app/api/tickets/route.ts"}]}