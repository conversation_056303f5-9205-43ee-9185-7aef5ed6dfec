{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "rich-text-editor", "type": "registry:component", "title": "Rich Text Editor", "description": "Feature-rich WYSIWYG text editor with toolbar, formatting options, image upload, link insertion, and export capabilities", "dependencies": [], "registryDependencies": ["button", "card", "separator", "select", "popover", "input", "label", "badge"], "files": [{"path": "lib/editor-utils.ts", "type": "registry:lib"}, {"path": "types/editor.ts", "type": "registry:type"}, {"path": "hooks/use-rich-text-editor.ts", "type": "registry:hook"}, {"path": "components/ui/rich-text-editor.tsx", "type": "registry:component"}, {"path": "components/ui/editor-toolbar.tsx", "type": "registry:component"}, {"path": "components/ui/editor-status-bar.tsx", "type": "registry:component"}]}