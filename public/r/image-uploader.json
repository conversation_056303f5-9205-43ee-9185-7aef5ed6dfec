{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "image-uploader", "type": "registry:component", "title": "Image Uploader", "description": "Complete image upload system with drag & drop, progress tracking, cloud storage integration (Cloudinary/S3), thumbnail generation, and image management", "dependencies": [], "registryDependencies": ["button", "card", "progress", "badge"], "files": [{"path": "lib/image-upload.ts", "type": "registry:lib"}, {"path": "types/upload.ts", "type": "registry:type"}, {"path": "hooks/use-image-upload.ts", "type": "registry:hook"}, {"path": "components/ui/image-uploader.tsx", "type": "registry:component"}, {"path": "components/ui/image-dropzone.tsx", "type": "registry:component"}, {"path": "components/ui/image-preview.tsx", "type": "registry:component"}, {"path": "actions/upload-actions.ts", "type": "registry:lib"}, {"path": "app/api/upload/route.ts", "type": "registry:lib", "target": "app/api/upload/route.ts"}]}