{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "customer-portal", "type": "registry:component", "title": "Stripe Customer Portal Module", "description": "Self-service customer portal for managing billing, subscriptions, and payment methods", "dependencies": ["stripe", "@stripe/stripe-js"], "registryDependencies": ["button", "card"], "files": [{"path": "lib/stripe.ts", "content": "/**\n * Supreme Toolkit - Stripe Configuration\n * \n * This file contains the Stripe client configuration and utilities\n * for the Supreme Toolkit payment module.\n */\n\nimport Stripe from 'stripe';\nimport { getModuleConfig } from '@/config';\n\n// ============================================================================\n// STRIPE CLIENT CONFIGURATION\n// ============================================================================\n\n/**\n * Server-side Stripe instance\n * Only use this on the server side (API routes, server actions)\n */\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2025-06-30.basil',\n  typescript: true,\n  maxNetworkRetries: 2, // Retry failed requests up to 2 times\n  timeout: 20 * 1000, // 20 second timeout\n  appInfo: {\n    name: 'Supreme Toolkit',\n    version: '1.0.0',\n    url: 'https://github.com/supreme-toolkit/supreme-toolkit',\n  },\n});\n\n/**\n * Get Stripe configuration from the toolkit config\n */\nexport function getStripeConfig() {\n  return getModuleConfig('stripe');\n}\n\n// ============================================================================\n// STRIPE UTILITIES\n// ============================================================================\n\n/**\n * Format price for display (converts cents to dollars)\n */\nexport function formatPrice(priceInCents: number, currency: string = 'usd'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n  }).format(priceInCents / 100);\n}\n\n/**\n * Convert dollars to cents for Stripe\n */\nexport function dollarsToCents(dollars: number): number {\n  return Math.round(dollars * 100);\n}\n\n/**\n * Convert cents to dollars\n */\nexport function centsToDollars(cents: number): number {\n  return cents / 100;\n}\n\n/**\n * Validate webhook signature\n */\nexport function validateWebhookSignature(\n  payload: string | Buffer,\n  signature: string,\n  webhookSecret: string\n): Stripe.Event {\n  try {\n    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);\n  } catch (error) {\n    throw new Error(`Webhook signature verification failed: ${error}`);\n  }\n}\n\n// ============================================================================\n// STRIPE TYPES\n// ============================================================================\n\nexport interface PaymentIntentData {\n  amount: number;\n  currency: string;\n  metadata?: Record<string, string>;\n  customerId?: string;\n  description?: string;\n}\n\nexport interface SubscriptionData {\n  customerId: string;\n  priceId: string;\n  metadata?: Record<string, string>;\n  trialPeriodDays?: number;\n}\n\nexport interface CustomerData {\n  email: string;\n  name?: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface PriceData {\n  productId: string;\n  unitAmount: number;\n  currency: string;\n  recurring?: {\n    interval: 'day' | 'week' | 'month' | 'year';\n    intervalCount?: number;\n  };\n}\n\n// ============================================================================\n// STRIPE HELPER FUNCTIONS\n// ============================================================================\n\n/**\n * Create a payment intent\n */\nexport async function createPaymentIntent(data: PaymentIntentData): Promise<Stripe.PaymentIntent> {\n  return await stripe.paymentIntents.create({\n    amount: data.amount,\n    currency: data.currency,\n    customer: data.customerId,\n    description: data.description,\n    metadata: data.metadata || {},\n    automatic_payment_methods: {\n      enabled: true,\n    },\n  });\n}\n\n/**\n * Create a customer\n */\nexport async function createCustomer(data: CustomerData): Promise<Stripe.Customer> {\n  return await stripe.customers.create({\n    email: data.email,\n    name: data.name,\n    metadata: data.metadata || {},\n  });\n}\n\n/**\n * Create a subscription\n */\nexport async function createSubscription(data: SubscriptionData): Promise<Stripe.Subscription> {\n  return await stripe.subscriptions.create({\n    customer: data.customerId,\n    items: [{ price: data.priceId }],\n    metadata: data.metadata || {},\n    trial_period_days: data.trialPeriodDays,\n  });\n}\n\n/**\n * Cancel a subscription\n */\nexport async function cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {\n  return await stripe.subscriptions.cancel(subscriptionId);\n}\n\n/**\n * Create a checkout session\n */\nexport async function createCheckoutSession(params: {\n  priceId: string;\n  customerId?: string;\n  successUrl: string;\n  cancelUrl: string;\n  mode?: 'payment' | 'subscription';\n  metadata?: Record<string, string>;\n}): Promise<Stripe.Checkout.Session> {\n  return await stripe.checkout.sessions.create({\n    mode: params.mode || 'payment',\n    customer: params.customerId,\n    line_items: [\n      {\n        price: params.priceId,\n        quantity: 1,\n      },\n    ],\n    success_url: params.successUrl,\n    cancel_url: params.cancelUrl,\n    metadata: params.metadata || {},\n  });\n}\n\n/**\n * Create a customer portal session\n */\nexport async function createCustomerPortalSession(\n  customerId: string,\n  returnUrl: string\n): Promise<Stripe.BillingPortal.Session> {\n  return await stripe.billingPortal.sessions.create({\n    customer: customerId,\n    return_url: returnUrl,\n  });\n}\n\n/**\n * Retrieve a customer by email\n */\nexport async function getCustomerByEmail(email: string): Promise<Stripe.Customer | null> {\n  const customers = await stripe.customers.list({\n    email: email,\n    limit: 1,\n  });\n  \n  return customers.data.length > 0 ? customers.data[0] : null;\n}\n\n/**\n * Get customer subscriptions\n */\nexport async function getCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {\n  const subscriptions = await stripe.subscriptions.list({\n    customer: customerId,\n    status: 'all',\n  });\n  \n  return subscriptions.data;\n}\n\n/**\n * Get active subscription for customer\n */\nexport async function getActiveSubscription(customerId: string): Promise<Stripe.Subscription | null> {\n  const subscriptions = await stripe.subscriptions.list({\n    customer: customerId,\n    status: 'active',\n    limit: 1,\n  });\n  \n  return subscriptions.data.length > 0 ? subscriptions.data[0] : null;\n}\n\n// ============================================================================\n// STRIPE WEBHOOK EVENTS\n// ============================================================================\n\nexport const STRIPE_WEBHOOK_EVENTS = {\n  PAYMENT_INTENT_SUCCEEDED: 'payment_intent.succeeded',\n  PAYMENT_INTENT_PAYMENT_FAILED: 'payment_intent.payment_failed',\n  CUSTOMER_SUBSCRIPTION_CREATED: 'customer.subscription.created',\n  CUSTOMER_SUBSCRIPTION_UPDATED: 'customer.subscription.updated',\n  CUSTOMER_SUBSCRIPTION_DELETED: 'customer.subscription.deleted',\n  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',\n  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',\n  INVOICE_FINALIZED: 'invoice.finalized',\n} as const;\n\nexport type StripeWebhookEvent = typeof STRIPE_WEBHOOK_EVENTS[keyof typeof STRIPE_WEBHOOK_EVENTS];\n", "type": "registry:lib"}, {"path": "hooks/use-stripe.ts", "content": "\"use client\";\n\n/**\n * Supreme Toolkit - Stripe Hooks\n * \n * Custom React hooks for managing Stripe payments, subscriptions,\n * and customer data in the Supreme Toolkit.\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { getModuleConfig } from '@/config';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PaymentIntent {\n  id: string;\n  amount: number;\n  currency: string;\n  status: string;\n  clientSecret: string;\n}\n\nexport interface Subscription {\n  id: string;\n  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  cancelAtPeriodEnd: boolean;\n  trialEnd?: Date;\n  plan: {\n    id: string;\n    name: string;\n    amount: number;\n    currency: string;\n    interval: string;\n  };\n  customer: {\n    id: string;\n    email: string;\n  };\n}\n\nexport interface Customer {\n  id: string;\n  email: string;\n  name?: string;\n  subscriptions: Subscription[];\n}\n\nexport interface CheckoutSession {\n  id: string;\n  url: string;\n  mode: 'payment' | 'subscription';\n  status: string;\n}\n\n// ============================================================================\n// USE STRIPE CONFIG HOOK\n// ============================================================================\n\n/**\n * Hook to get Stripe configuration\n */\nexport function useStripeConfig() {\n  const [config, setConfig] = useState(() => {\n    try {\n      return getModuleConfig('stripe');\n    } catch {\n      return null;\n    }\n  });\n\n  return config;\n}\n\n// ============================================================================\n// USE CHECKOUT HOOK\n// ============================================================================\n\n/**\n * Hook for managing Stripe checkout sessions\n */\nexport function useCheckout() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createCheckoutSession = useCallback(async (params: {\n    priceId: string;\n    mode?: 'payment' | 'subscription';\n    successUrl?: string;\n    cancelUrl?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n  }) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/create-checkout-session', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          priceId: params.priceId,\n          mode: params.mode || 'payment',\n          successUrl: params.successUrl || `${window.location.origin}/payment/success`,\n          cancelUrl: params.cancelUrl || `${window.location.origin}/payment/cancel`,\n          customerId: params.customerId,\n          metadata: params.metadata,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(errorText || 'Failed to create checkout session');\n      }\n\n      const session: CheckoutSession = await response.json();\n      return session;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Checkout failed';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const redirectToCheckout = useCallback(async (params: {\n    priceId: string;\n    mode?: 'payment' | 'subscription';\n    successUrl?: string;\n    cancelUrl?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n  }) => {\n    try {\n      const session = await createCheckoutSession(params);\n      if (session.url) {\n        window.location.href = session.url;\n      }\n      return session;\n    } catch (err) {\n      throw err;\n    }\n  }, [createCheckoutSession]);\n\n  return {\n    createCheckoutSession,\n    redirectToCheckout,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USE SUBSCRIPTION HOOK\n// ============================================================================\n\n/**\n * Hook for managing user subscriptions\n */\nexport function useSubscription(customerId?: string) {\n  const [subscription, setSubscription] = useState<Subscription | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch subscription data\n  const fetchSubscription = useCallback(async () => {\n    if (!customerId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(`/api/stripe/subscription?customerId=${customerId}`);\n      \n      if (!response.ok) {\n        if (response.status === 404) {\n          setSubscription(null);\n          return;\n        }\n        throw new Error('Failed to fetch subscription');\n      }\n\n      const data = await response.json();\n      setSubscription(data);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch subscription';\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [customerId]);\n\n  // Cancel subscription\n  const cancelSubscription = useCallback(async (subscriptionId: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/cancel-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ subscriptionId }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to cancel subscription');\n      }\n\n      // Refresh subscription data\n      await fetchSubscription();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel subscription';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchSubscription]);\n\n  // Reactivate subscription\n  const reactivateSubscription = useCallback(async (subscriptionId: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/reactivate-subscription', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ subscriptionId }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to reactivate subscription');\n      }\n\n      // Refresh subscription data\n      await fetchSubscription();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to reactivate subscription';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchSubscription]);\n\n  // Load subscription on mount and when customerId changes\n  useEffect(() => {\n    fetchSubscription();\n  }, [fetchSubscription]);\n\n  return {\n    subscription,\n    loading,\n    error,\n    refetch: fetchSubscription,\n    cancelSubscription,\n    reactivateSubscription,\n  };\n}\n\n// ============================================================================\n// USE CUSTOMER PORTAL HOOK\n// ============================================================================\n\n/**\n * Hook for managing Stripe customer portal\n */\nexport function useCustomerPortal() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createPortalSession = useCallback(async (customerId: string, returnUrl?: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/customer-portal', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          customerId,\n          returnUrl: returnUrl || window.location.href,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create customer portal session');\n      }\n\n      const { url } = await response.json();\n      return url;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create portal session';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const redirectToPortal = useCallback(async (customerId: string, returnUrl?: string) => {\n    try {\n      const url = await createPortalSession(customerId, returnUrl);\n      if (url) {\n        window.location.href = url;\n      }\n    } catch (err) {\n      throw err;\n    }\n  }, [createPortalSession]);\n\n  return {\n    createPortalSession,\n    redirectToPortal,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USE PAYMENT INTENT HOOK\n// ============================================================================\n\n/**\n * Hook for managing payment intents\n */\nexport function usePaymentIntent() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const createPaymentIntent = useCallback(async (params: {\n    amount: number;\n    currency?: string;\n    customerId?: string;\n    metadata?: Record<string, string>;\n    description?: string;\n  }) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          amount: params.amount,\n          currency: params.currency || 'usd',\n          customerId: params.customerId,\n          metadata: params.metadata,\n          description: params.description,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create payment intent');\n      }\n\n      const paymentIntent: PaymentIntent = await response.json();\n      return paymentIntent;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create payment intent';\n      setError(errorMessage);\n      throw new Error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createPaymentIntent,\n    loading,\n    error,\n  };\n}\n\n// ============================================================================\n// USAGE EXAMPLES\n// ============================================================================\n\n/*\n// Using checkout hook\nconst { redirectToCheckout, loading, error } = useCheckout();\n\nconst handlePurchase = async () => {\n  try {\n    await redirectToCheckout({\n      priceId: 'price_1234567890',\n      mode: 'payment',\n      successUrl: '/success',\n      cancelUrl: '/cancel',\n    });\n  } catch (err) {\n    console.error('Checkout failed:', err);\n  }\n};\n\n// Using subscription hook\nconst { subscription, loading, cancelSubscription } = useSubscription(customerId);\n\n// Using customer portal hook\nconst { redirectToPortal } = useCustomerPortal();\n\nconst handleManageSubscription = async () => {\n  try {\n    await redirectToPortal(customerId);\n  } catch (err) {\n    console.error('Portal failed:', err);\n  }\n};\n*/\n", "type": "registry:hook"}, {"path": "actions/stripe-actions.ts", "content": "\"use server\";\n\n/**\n * Supreme Toolkit - Stripe Server Actions\n * \n * Server actions for handling Stripe payment events and operations.\n * These actions are triggered by webhooks and user interactions.\n */\n\nimport { stripe } from '@/lib/stripe';\nimport Stripe from 'stripe';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\nexport interface PaymentCompleteData {\n  paymentIntentId: string;\n  customerId: string;\n  amount: number;\n  currency: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface SubscriptionCreatedData {\n  subscriptionId: string;\n  customerId: string;\n  priceId: string;\n  status: string;\n  currentPeriodStart: Date;\n  currentPeriodEnd: Date;\n  metadata?: Record<string, string>;\n}\n\nexport interface SubscriptionCancelledData {\n  subscriptionId: string;\n  customerId: string;\n  canceledAt: Date;\n  cancelAtPeriodEnd: boolean;\n  metadata?: Record<string, string>;\n}\n\nexport interface PaymentFailedData {\n  paymentIntentId: string;\n  customerId?: string;\n  amount: number;\n  currency: string;\n  failureCode?: string;\n  failureMessage?: string;\n  metadata?: Record<string, string>;\n}\n\nexport interface InvoiceGeneratedData {\n  invoiceId: string;\n  customerId: string;\n  subscriptionId?: string;\n  amount: number;\n  currency: string;\n  status: string;\n  dueDate?: Date;\n  metadata?: Record<string, string>;\n}\n\n// ============================================================================\n// PAYMENT EVENTS\n// ============================================================================\n\n/**\n * Called when a payment is successfully completed\n * Customize this function to handle successful payments in your app\n */\nexport async function onPaymentComplete(data: PaymentCompleteData) {\n  try {\n    console.log('Payment completed:', data);\n\n    // Example: Update user's account status\n    // await updateUserPremiumStatus(data.customerId, true);\n\n    // Example: Send confirmation email\n    // await sendPaymentConfirmationEmail(data.customerId, data.amount);\n\n    // Example: Log the payment for analytics\n    // await logPaymentEvent('payment_completed', data);\n\n    // Example: Grant access to paid features\n    // await grantPremiumAccess(data.customerId);\n\n    // You can customize this function based on your app's needs\n    return {\n      success: true,\n      message: 'Payment processed successfully',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onPaymentComplete:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Called when a payment fails\n * Customize this function to handle failed payments in your app\n */\nexport async function onPaymentFailed(data: PaymentFailedData) {\n  try {\n    console.log('Payment failed:', data);\n\n    // Example: Send failure notification email\n    // await sendPaymentFailureEmail(data.customerId, data.failureMessage);\n\n    // Example: Log the failure for analytics\n    // await logPaymentEvent('payment_failed', data);\n\n    // Example: Retry payment logic\n    // await schedulePaymentRetry(data.paymentIntentId);\n\n    return {\n      success: true,\n      message: 'Payment failure handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onPaymentFailed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// SUBSCRIPTION EVENTS\n// ============================================================================\n\n/**\n * Called when a new subscription is created\n * Customize this function to handle new subscriptions in your app\n */\nexport async function onSubscriptionCreated(data: SubscriptionCreatedData) {\n  try {\n    console.log('Subscription created:', data);\n\n    // Example: Update user's subscription status\n    // await updateUserSubscription(data.customerId, {\n    //   subscriptionId: data.subscriptionId,\n    //   status: 'active',\n    //   plan: data.priceId,\n    //   currentPeriodEnd: data.currentPeriodEnd,\n    // });\n\n    // Example: Send welcome email\n    // await sendSubscriptionWelcomeEmail(data.customerId);\n\n    // Example: Grant subscription features\n    // await grantSubscriptionAccess(data.customerId, data.priceId);\n\n    // Example: Log subscription event\n    // await logSubscriptionEvent('subscription_created', data);\n\n    return {\n      success: true,\n      message: 'Subscription created successfully',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onSubscriptionCreated:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Called when a subscription is cancelled\n * Customize this function to handle subscription cancellations in your app\n */\nexport async function onSubscriptionCancelled(data: SubscriptionCancelledData) {\n  try {\n    console.log('Subscription cancelled:', data);\n\n    // Example: Update user's subscription status\n    // await updateUserSubscription(data.customerId, {\n    //   subscriptionId: data.subscriptionId,\n    //   status: 'cancelled',\n    //   canceledAt: data.canceledAt,\n    //   cancelAtPeriodEnd: data.cancelAtPeriodEnd,\n    // });\n\n    // Example: Send cancellation confirmation email\n    // await sendSubscriptionCancellationEmail(data.customerId);\n\n    // Example: Schedule access removal (if cancel at period end)\n    // if (data.cancelAtPeriodEnd) {\n    //   await scheduleAccessRemoval(data.customerId, data.canceledAt);\n    // } else {\n    //   await revokeSubscriptionAccess(data.customerId);\n    // }\n\n    // Example: Log cancellation event\n    // await logSubscriptionEvent('subscription_cancelled', data);\n\n    return {\n      success: true,\n      message: 'Subscription cancellation handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onSubscriptionCancelled:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// INVOICE EVENTS\n// ============================================================================\n\n/**\n * Called when an invoice is generated\n * Customize this function to handle invoice generation in your app\n */\nexport async function onInvoiceGenerated(data: InvoiceGeneratedData) {\n  try {\n    console.log('Invoice generated:', data);\n\n    // Example: Send invoice email to customer\n    // await sendInvoiceEmail(data.customerId, data.invoiceId);\n\n    // Example: Update billing records\n    // await updateBillingRecord(data.customerId, {\n    //   invoiceId: data.invoiceId,\n    //   amount: data.amount,\n    //   status: data.status,\n    //   dueDate: data.dueDate,\n    // });\n\n    // Example: Log invoice event\n    // await logInvoiceEvent('invoice_generated', data);\n\n    return {\n      success: true,\n      message: 'Invoice generation handled',\n      data,\n    };\n  } catch (error) {\n    console.error('Error in onInvoiceGenerated:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// UTILITY FUNCTIONS\n// ============================================================================\n\n/**\n * Create a customer in Stripe\n */\nexport async function createStripeCustomer(email: string, name?: string, metadata?: Record<string, string>) {\n  try {\n    const customer = await stripe.customers.create({\n      email,\n      name,\n      metadata: metadata || {},\n    });\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error creating Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Update a customer in Stripe\n */\nexport async function updateStripeCustomer(\n  customerId: string, \n  updates: { email?: string; name?: string; metadata?: Record<string, string> }\n) {\n  try {\n    const customer = await stripe.customers.update(customerId, updates);\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error updating Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Get customer by email\n */\nexport async function getStripeCustomerByEmail(email: string) {\n  try {\n    const customers = await stripe.customers.list({\n      email: email,\n      limit: 1,\n    });\n\n    const customer = customers.data.length > 0 ? customers.data[0] : null;\n\n    return {\n      success: true,\n      customer,\n    };\n  } catch (error) {\n    console.error('Error getting Stripe customer:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Cancel a subscription\n */\nexport async function cancelStripeSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true) {\n  try {\n    let subscription: Stripe.Subscription;\n\n    if (cancelAtPeriodEnd) {\n      subscription = await stripe.subscriptions.update(subscriptionId, {\n        cancel_at_period_end: true,\n      });\n    } else {\n      subscription = await stripe.subscriptions.cancel(subscriptionId);\n    }\n\n    return {\n      success: true,\n      subscription,\n    };\n  } catch (error) {\n    console.error('Error cancelling Stripe subscription:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n/**\n * Reactivate a subscription\n */\nexport async function reactivateStripeSubscription(subscriptionId: string) {\n  try {\n    const subscription = await stripe.subscriptions.update(subscriptionId, {\n      cancel_at_period_end: false,\n    });\n\n    return {\n      success: true,\n      subscription,\n    };\n  } catch (error) {\n    console.error('Error reactivating Stripe subscription:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n\n// ============================================================================\n// WEBHOOK PROCESSING\n// ============================================================================\n\n/**\n * Process Stripe webhook events\n * This function routes webhook events to the appropriate handlers\n */\nexport async function processStripeWebhook(event: Stripe.Event) {\n  try {\n    switch (event.type) {\n      case 'payment_intent.succeeded': {\n        const paymentIntent = event.data.object as Stripe.PaymentIntent;\n        await onPaymentComplete({\n          paymentIntentId: paymentIntent.id,\n          customerId: paymentIntent.customer as string,\n          amount: paymentIntent.amount,\n          currency: paymentIntent.currency,\n          metadata: paymentIntent.metadata,\n        });\n        break;\n      }\n\n      case 'payment_intent.payment_failed': {\n        const paymentIntent = event.data.object as Stripe.PaymentIntent;\n        await onPaymentFailed({\n          paymentIntentId: paymentIntent.id,\n          customerId: paymentIntent.customer as string,\n          amount: paymentIntent.amount,\n          currency: paymentIntent.currency,\n          failureCode: paymentIntent.last_payment_error?.code,\n          failureMessage: paymentIntent.last_payment_error?.message,\n          metadata: paymentIntent.metadata,\n        });\n        break;\n      }\n\n      case 'customer.subscription.created': {\n        const subscription = event.data.object as Stripe.Subscription;\n        await onSubscriptionCreated({\n          subscriptionId: subscription.id,\n          customerId: subscription.customer as string,\n          priceId: subscription.items.data[0].price.id,\n          status: subscription.status,\n          currentPeriodStart: new Date((subscription as any).current_period_start * 1000),\n          currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),\n          metadata: subscription.metadata || {},\n        });\n        break;\n      }\n\n      case 'customer.subscription.deleted': {\n        const subscription = event.data.object as Stripe.Subscription;\n        await onSubscriptionCancelled({\n          subscriptionId: subscription.id,\n          customerId: subscription.customer as string,\n          canceledAt: new Date((subscription as any).canceled_at! * 1000),\n          cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,\n          metadata: subscription.metadata || {},\n        });\n        break;\n      }\n\n      case 'invoice.finalized': {\n        const invoice = event.data.object as Stripe.Invoice;\n        await onInvoiceGenerated({\n          invoiceId: invoice.id!,\n          customerId: invoice.customer as string,\n          subscriptionId: (invoice as any).subscription as string || undefined,\n          amount: invoice.amount_due,\n          currency: invoice.currency,\n          status: invoice.status!,\n          dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : undefined,\n          metadata: invoice.metadata || {},\n        });\n        break;\n      }\n\n      default:\n        console.log(`Unhandled event type: ${event.type}`);\n    }\n\n    return { success: true };\n  } catch (error) {\n    console.error('Error processing webhook:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n    };\n  }\n}\n", "type": "registry:lib"}, {"path": "app/api/stripe/customer-portal/route.ts", "content": "/**\n * Supreme Toolkit - Customer Portal API Route\n * \n * Creates a Stripe customer portal session for subscription management.\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { createCustomerPortalSession } from '@/lib/stripe';\n\n// ============================================================================\n// TYPES\n// ============================================================================\n\ninterface CreatePortalSessionRequest {\n  customerId: string;\n  returnUrl: string;\n}\n\n// ============================================================================\n// API HANDLER\n// ============================================================================\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Parse request body\n    const body: CreatePortalSessionRequest = await request.json();\n    \n    // Validate required fields\n    if (!body.customerId) {\n      return NextResponse.json(\n        { error: 'Customer ID is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!body.returnUrl) {\n      return NextResponse.json(\n        { error: 'Return URL is required' },\n        { status: 400 }\n      );\n    }\n\n    // Create customer portal session\n    const session = await createCustomerPortalSession(\n      body.customerId,\n      body.returnUrl\n    );\n\n    // Return session URL\n    return NextResponse.json({\n      url: session.url,\n    });\n\n  } catch (error) {\n    console.error('Create customer portal session error:', error);\n    \n    return NextResponse.json(\n      { \n        error: error instanceof Error ? error.message : 'Failed to create customer portal session' \n      },\n      { status: 500 }\n    );\n  }\n}\n\n// ============================================================================\n// SUPPORTED METHODS\n// ============================================================================\n\nexport async function GET() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n", "type": "registry:lib", "target": "app/api/stripe/customer-portal/route.ts"}]}