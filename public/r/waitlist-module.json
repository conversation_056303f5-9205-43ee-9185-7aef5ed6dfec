{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "waitlist-module", "type": "registry:component", "title": "Waitlist Component", "description": "Complete waitlist system with email collection, validation, duplicate checking, and email confirmations", "dependencies": ["zod"], "registryDependencies": ["button", "input", "label", "card"], "files": [{"path": "components/ui/waitlist-form.tsx", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { useWaitlist } from \"@/hooks/use-waitlist\";\nimport { Loader2, CheckCircle, AlertCircle } from \"lucide-react\";\n\ninterface WaitlistFormProps {\n  title?: string;\n  description?: string;\n  placeholder?: string;\n  buttonText?: string;\n  className?: string;\n}\n\nexport function WaitlistForm({\n  title = \"Join the Waitlist\",\n  description = \"Be the first to know when we launch!\",\n  placeholder = \"Enter your email address\",\n  buttonText = \"Join Waitlist\",\n  className,\n}: WaitlistFormProps) {\n  const [email, setEmail] = useState(\"\");\n  const { subscribe, isLoading, error, success } = useWaitlist();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n    \n    await subscribe({ email });\n    if (success) {\n      setEmail(\"\");\n    }\n  };\n\n  if (success) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-center justify-center space-x-2 text-green-600\">\n            <CheckCircle className=\"h-5 w-5\" />\n            <p className=\"text-sm font-medium\">Successfully joined the waitlist!</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle>{title}</CardTitle>\n        <CardDescription>{description}</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder={placeholder}\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              disabled={isLoading}\n            />\n          </div>\n          \n          {error && (\n            <div className=\"flex items-center space-x-2 text-red-600\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <p className=\"text-sm\">{error}</p>\n            </div>\n          )}\n          \n          <Button type=\"submit\" disabled={isLoading || !email} className=\"w-full\">\n            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {buttonText}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}", "type": "registry:component"}, {"path": "hooks/use-waitlist.ts", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { getModuleConfig } from \"@/config\";\nimport { checkIfAlreadyInWaitlist, onAddWaitlist, getWaitlistEntry } from \"@/actions/waitlist-actions\";\n\ninterface UseWaitlistOptions {\n  onSuccess?: (data: any) => void;\n  onError?: (error: string) => void;\n}\n\ninterface WaitlistSubscribeParams {\n  email: string;\n  name?: string;\n  referralCode?: string;\n}\n\nexport function useWaitlist(options: UseWaitlistOptions = {}) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState(false);\n  const [data, setData] = useState<any>(null);\n\n  const config = getModuleConfig('waitlist');\n\n  const subscribe = async (params: WaitlistSubscribeParams) => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(false);\n\n    try {\n      // Use server action directly for better performance and consistency\n      const result = await onAddWaitlist({\n        email: params.email,\n        name: params.name,\n        referralCode: params.referralCode,\n      });\n\n      if (!result.success) {\n        throw new Error(result.error || 'Failed to join waitlist');\n      }\n\n      const responseData = {\n        id: result.entry!.id,\n        position: result.entry!.position,\n        status: result.entry!.status,\n      };\n\n      setData(responseData);\n      setSuccess(true);\n\n      options.onSuccess?.(responseData);\n\n      // Redirect if configured\n      if (config?.successRedirect) {\n        window.location.href = config.successRedirect;\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const checkStatus = async (email: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await getWaitlistEntry(email);\n\n      if (!result.success) {\n        throw new Error(result.error || 'Entry not found');\n      }\n\n      const responseData = {\n        id: result.entry!.id,\n        position: result.entry!.position,\n        status: result.entry!.status,\n        createdAt: result.entry!.createdAt,\n      };\n\n      setData(responseData);\n      return responseData;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const checkIfExists = async (email: string) => {\n    try {\n      const result = await checkIfAlreadyInWaitlist(email);\n      return result.exists;\n    } catch (err) {\n      console.error('Error checking waitlist:', err);\n      return false;\n    }\n  };\n\n  const reset = () => {\n    setError(null);\n    setSuccess(false);\n    setData(null);\n  };\n\n  return {\n    subscribe,\n    checkStatus,\n    checkIfExists,\n    reset,\n    isLoading,\n    error,\n    success,\n    data,\n  };\n}", "type": "registry:hook"}, {"path": "actions/waitlist-actions.ts", "content": "\"use server\";\n\nimport { WaitlistEntry } from \"@/types\";\nimport { sendWaitlistWelcomeEmail, sendWaitlistApprovalEmail } from \"@/lib/mailer\";\n\n// ============================================================================\n// WAITLIST SERVER ACTIONS\n// ============================================================================\n\n// Simple in-memory store for demo - replace with your database\nconst waitlistStore: WaitlistEntry[] = [];\nlet nextId = 1;\n\n/**\n * Check if email is already in waitlist\n */\nexport async function checkIfAlreadyInWaitlist(email: string): Promise<{\n  exists: boolean;\n  entry?: WaitlistEntry;\n}> {\n  try {\n    // In a real app, this would query your database\n    const existingEntry = waitlistStore.find(entry => entry.email.toLowerCase() === email.toLowerCase());\n\n    return {\n      exists: !!existingEntry,\n      entry: existingEntry,\n    };\n  } catch (error) {\n    console.error('Error checking waitlist:', error);\n    throw new Error('Failed to check waitlist status');\n  }\n}\n\n/**\n * Add user to waitlist with proper validation and email confirmation\n */\nexport async function onAddWaitlist(params: {\n  email: string;\n  name?: string;\n  referralCode?: string;\n}): Promise<{\n  success: boolean;\n  entry?: WaitlistEntry;\n  error?: string;\n}> {\n  try {\n    // Validate email format\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(params.email)) {\n      return {\n        success: false,\n        error: 'Please enter a valid email address',\n      };\n    }\n\n    // Check if already exists\n    const existingCheck = await checkIfAlreadyInWaitlist(params.email);\n    if (existingCheck.exists) {\n      return {\n        success: false,\n        error: 'This email is already on the waitlist',\n        entry: existingCheck.entry,\n      };\n    }\n\n    // Create new waitlist entry\n    const entry: WaitlistEntry = {\n      id: nextId.toString(),\n      email: params.email.toLowerCase(),\n      name: params.name,\n      referralCode: params.referralCode,\n      status: 'pending',\n      position: waitlistStore.length + 1,\n      createdAt: new Date(),\n    };\n\n    // Add to store (replace with database save)\n    waitlistStore.push(entry);\n    nextId++;\n\n    // Send welcome email\n    try {\n      const emailResult = await sendWaitlistWelcomeEmail(\n        entry.email,\n        entry.name,\n        entry.position\n      );\n\n      if (!emailResult.success) {\n        console.warn('Failed to send welcome email:', emailResult.error);\n        // Don't fail the signup if email fails\n      }\n    } catch (emailError) {\n      console.warn('Email sending error:', emailError);\n      // Continue with signup even if email fails\n    }\n\n    // Call the signup event handler\n    await onWaitlistSignup({\n      email: entry.email,\n      name: entry.name,\n      referralCode: entry.referralCode,\n      timestamp: entry.createdAt,\n    });\n\n    return {\n      success: true,\n      entry,\n    };\n  } catch (error) {\n    console.error('Error adding to waitlist:', error);\n    return {\n      success: false,\n      error: 'Failed to add to waitlist. Please try again.',\n    };\n  }\n}\n\n/**\n * Called when a user signs up for the waitlist\n * Customize this function with your own business logic\n */\nexport async function onWaitlistSignup(params: {\n  email: string;\n  name?: string;\n  referralCode?: string;\n  timestamp: Date;\n}) {\n  console.log('New waitlist signup:', params);\n\n  // Add your custom business logic here:\n\n  // 1. Track analytics event\n  try {\n    // Example: await analytics.track('waitlist_signup', {\n    //   email: params.email,\n    //   name: params.name,\n    //   referralCode: params.referralCode,\n    //   timestamp: params.timestamp,\n    // });\n  } catch (error) {\n    console.warn('Analytics tracking failed:', error);\n  }\n\n  // 2. Add to CRM/Marketing platform\n  try {\n    // Example: await crm.addContact({\n    //   email: params.email,\n    //   name: params.name,\n    //   tags: ['waitlist'],\n    //   customFields: {\n    //     referralCode: params.referralCode,\n    //     signupDate: params.timestamp,\n    //   },\n    // });\n  } catch (error) {\n    console.warn('CRM integration failed:', error);\n  }\n\n  // 3. Send notification to team\n  try {\n    // Example: await slack.sendMessage({\n    //   channel: '#waitlist',\n    //   text: `New waitlist signup: ${params.email} ${params.name ? `(${params.name})` : ''}`,\n    // });\n  } catch (error) {\n    console.warn('Team notification failed:', error);\n  }\n\n  // 4. Update referral counts if applicable\n  if (params.referralCode) {\n    try {\n      // Example: await updateReferralCount(params.referralCode);\n    } catch (error) {\n      console.warn('Referral tracking failed:', error);\n    }\n  }\n}\n\n/**\n * Approve a waitlist entry\n */\nexport async function approveWaitlistEntry(entryId: string, approvedBy?: string): Promise<{\n  success: boolean;\n  entry?: WaitlistEntry;\n  error?: string;\n}> {\n  try {\n    // Find the entry\n    const entryIndex = waitlistStore.findIndex(entry => entry.id === entryId);\n    if (entryIndex === -1) {\n      return {\n        success: false,\n        error: 'Waitlist entry not found',\n      };\n    }\n\n    const entry = waitlistStore[entryIndex];\n\n    // Update entry status\n    entry.status = 'approved';\n    entry.approvedAt = new Date();\n\n    // Send approval email\n    try {\n      const emailResult = await sendWaitlistApprovalEmail(entry.email, entry.name);\n      if (!emailResult.success) {\n        console.warn('Failed to send approval email:', emailResult.error);\n      }\n    } catch (emailError) {\n      console.warn('Approval email error:', emailError);\n    }\n\n    // Call the approval event handler\n    await onWaitlistApproval({\n      entry,\n      approvedBy,\n      timestamp: new Date(),\n    });\n\n    return {\n      success: true,\n      entry,\n    };\n  } catch (error) {\n    console.error('Error approving waitlist entry:', error);\n    return {\n      success: false,\n      error: 'Failed to approve waitlist entry',\n    };\n  }\n}\n\n/**\n * Called when a waitlist entry is approved\n */\nexport async function onWaitlistApproval(params: {\n  entry: WaitlistEntry;\n  approvedBy?: string;\n  timestamp: Date;\n}) {\n  console.log('Waitlist entry approved:', params.entry.email);\n\n  // Add your custom business logic here:\n\n  // 1. Create user account automatically\n  try {\n    // Example: await createUserAccount({\n    //   email: params.entry.email,\n    //   name: params.entry.name,\n    //   source: 'waitlist',\n    // });\n  } catch (error) {\n    console.warn('User account creation failed:', error);\n  }\n\n  // 2. Add to onboarding sequence\n  try {\n    // Example: await addToOnboardingSequence(params.entry.email);\n  } catch (error) {\n    console.warn('Onboarding sequence failed:', error);\n  }\n\n  // 3. Track analytics\n  try {\n    // Example: await analytics.track('waitlist_approved', {\n    //   email: params.entry.email,\n    //   approvedBy: params.approvedBy,\n    //   waitTime: params.timestamp.getTime() - params.entry.createdAt.getTime(),\n    // });\n  } catch (error) {\n    console.warn('Analytics tracking failed:', error);\n  }\n\n  // 4. Notify team\n  try {\n    // Example: await slack.sendMessage({\n    //   channel: '#approvals',\n    //   text: `Waitlist entry approved: ${params.entry.email} by ${params.approvedBy || 'system'}`,\n    // });\n  } catch (error) {\n    console.warn('Team notification failed:', error);\n  }\n}\n\n/**\n * Get waitlist entry by email\n */\nexport async function getWaitlistEntry(email: string): Promise<{\n  success: boolean;\n  entry?: WaitlistEntry;\n  error?: string;\n}> {\n  try {\n    const entry = waitlistStore.find(e => e.email.toLowerCase() === email.toLowerCase());\n\n    if (!entry) {\n      return {\n        success: false,\n        error: 'Entry not found',\n      };\n    }\n\n    return {\n      success: true,\n      entry,\n    };\n  } catch (error) {\n    console.error('Error getting waitlist entry:', error);\n    return {\n      success: false,\n      error: 'Failed to get waitlist entry',\n    };\n  }\n}\n\n/**\n * Get waitlist statistics\n */\nexport async function getWaitlistStats(): Promise<{\n  total: number;\n  pending: number;\n  approved: number;\n  rejected: number;\n}> {\n  try {\n    return {\n      total: waitlistStore.length,\n      pending: waitlistStore.filter(e => e.status === 'pending').length,\n      approved: waitlistStore.filter(e => e.status === 'approved').length,\n      rejected: waitlistStore.filter(e => e.status === 'rejected').length,\n    };\n  } catch (error) {\n    console.error('Error getting waitlist stats:', error);\n    return { total: 0, pending: 0, approved: 0, rejected: 0 };\n  }\n}\n\n/**\n * Called when a waitlist entry is rejected\n */\nexport async function onWaitlistRejection(params: {\n  entry: WaitlistEntry;\n  rejectedBy?: string;\n  reason?: string;\n  timestamp: Date;\n}) {\n  console.log('Waitlist entry rejected:', params.entry.email);\n\n  // Add your custom business logic here:\n\n  // 1. Send rejection email (optional)\n  try {\n    // Example: await sendRejectionEmail(params.entry.email, params.entry.name, params.reason);\n  } catch (error) {\n    console.warn('Rejection email failed:', error);\n  }\n\n  // 2. Track analytics with reason\n  try {\n    // Example: await analytics.track('waitlist_rejected', {\n    //   email: params.entry.email,\n    //   rejectedBy: params.rejectedBy,\n    //   reason: params.reason,\n    //   waitTime: params.timestamp.getTime() - params.entry.createdAt.getTime(),\n    // });\n  } catch (error) {\n    console.warn('Analytics tracking failed:', error);\n  }\n\n  // 3. Notify team\n  try {\n    // Example: await slack.sendMessage({\n    //   channel: '#rejections',\n    //   text: `Waitlist entry rejected: ${params.entry.email} by ${params.rejectedBy || 'system'}. Reason: ${params.reason || 'Not specified'}`,\n    // });\n  } catch (error) {\n    console.warn('Team notification failed:', error);\n  }\n}", "type": "registry:lib"}, {"path": "app/api/waitlist/route.ts", "content": "import { NextRequest, NextResponse } from \"next/server\";\nimport { onAddWaitlist, getWaitlistEntry, getWaitlistStats } from \"@/actions/waitlist-actions\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, name, referralCode } = body;\n\n    if (!email) {\n      return NextResponse.json(\n        { error: \"Email is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Use the server action to add to waitlist\n    const result = await onAddWaitlist({\n      email,\n      name,\n      referralCode,\n    });\n\n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: result.error === 'This email is already on the waitlist' ? 409 : 400 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        id: result.entry!.id,\n        position: result.entry!.position,\n        status: result.entry!.status,\n      },\n    });\n  } catch (error) {\n    console.error('Waitlist API error:', error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const email = searchParams.get('email');\n\n    if (email) {\n      // Get specific entry using server action\n      const result = await getWaitlistEntry(email);\n\n      if (!result.success) {\n        return NextResponse.json(\n          { error: result.error },\n          { status: 404 }\n        );\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          id: result.entry!.id,\n          position: result.entry!.position,\n          status: result.entry!.status,\n          createdAt: result.entry!.createdAt,\n        },\n      });\n    }\n\n    // Get waitlist statistics (admin only - you'd add auth here)\n    const stats = await getWaitlistStats();\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        stats,\n        message: 'Use ?email=<EMAIL> to get specific entry details',\n      },\n    });\n  } catch (error) {\n    console.error('Waitlist API error:', error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}", "type": "registry:lib", "target": "app/api/waitlist/route.ts"}]}