{"$schema": "https://ui.shadcn.com/schema/registry.json", "name": "supreme-toolkit", "homepage": "https://supreme.jashagrawal.in", "items": [{"name": "auth-module", "type": "registry:component", "title": "Authentication Module", "description": "Complete authentication system with betterAuth, multiple providers (Google, GitHub, email/password), and auth guards", "dependencies": ["better-auth", "better-sqlite3"], "devDependencies": ["@types/better-sqlite3"], "registryDependencies": ["button", "input", "label", "card", "avatar", "separator"], "files": [{"path": "lib/auth.ts", "type": "registry:lib"}, {"path": "lib/auth-client.ts", "type": "registry:lib"}, {"path": "hooks/use-auth.ts", "type": "registry:hook"}, {"path": "components/ui/auth-signin.tsx", "type": "registry:component"}, {"path": "components/ui/auth-signup.tsx", "type": "registry:component"}, {"path": "components/ui/auth-signout.tsx", "type": "registry:component"}, {"path": "components/ui/auth-guards.tsx", "type": "registry:component"}, {"path": "components/ui/user-profile.tsx", "type": "registry:component"}, {"path": "actions/auth-actions.ts", "type": "registry:lib"}, {"path": "app/api/auth/[...all]/route.ts", "type": "registry:lib", "target": "app/api/auth/[...all]/route.ts"}]}, {"name": "mailer-module", "type": "registry:component", "title": "Universal Mailer Module", "description": "Unified email system with auto-detection for Resend and Nodemailer, email templates, and testing components", "dependencies": ["resend", "nodemailer", "@react-email/components"], "registryDependencies": ["button", "input", "label", "card", "textarea"], "files": [{"path": "lib/mailer.ts", "type": "registry:lib"}, {"path": "lib/mailer-resend.ts", "type": "registry:lib"}, {"path": "lib/mailer-nodemailer.ts", "type": "registry:lib"}, {"path": "hooks/use-mailer.ts", "type": "registry:hook"}, {"path": "components/ui/email-templates.tsx", "type": "registry:component"}, {"path": "components/ui/mailer-test.tsx", "type": "registry:component"}, {"path": "actions/mailer-actions.ts", "type": "registry:lib"}]}, {"name": "waitlist-module", "type": "registry:component", "title": "Waitlist Component", "description": "Complete waitlist system with email collection, validation, duplicate checking, and email confirmations", "dependencies": ["zod"], "registryDependencies": ["button", "input", "label", "card"], "files": [{"path": "components/ui/waitlist-form.tsx", "type": "registry:component"}, {"path": "hooks/use-waitlist.ts", "type": "registry:hook"}, {"path": "actions/waitlist-actions.ts", "type": "registry:lib"}, {"path": "app/api/waitlist/route.ts", "type": "registry:lib", "target": "app/api/waitlist/route.ts"}]}, {"name": "one-time-payment", "type": "registry:component", "title": "One-Time Payment Module", "description": "Stripe integration for one-time payments with checkout sessions and payment processing", "dependencies": ["stripe", "@stripe/stripe-js"], "registryDependencies": ["button", "card"], "files": [{"path": "lib/stripe.ts", "type": "registry:lib"}, {"path": "hooks/use-stripe.ts", "type": "registry:hook"}, {"path": "components/ui/pay-button.tsx", "type": "registry:component"}, {"path": "actions/stripe-actions.ts", "type": "registry:lib"}, {"path": "app/api/stripe/create-checkout-session/route.ts", "type": "registry:lib", "target": "app/api/stripe/create-checkout-session/route.ts"}, {"path": "app/api/stripe/webhooks/route.ts", "type": "registry:lib", "target": "app/api/stripe/webhooks/route.ts"}]}, {"name": "subscriptions", "type": "registry:component", "title": "Stripe Subscriptions Module", "description": "Complete subscription management with pricing plans, subscription lifecycle, and recurring billing", "dependencies": ["stripe", "@stripe/stripe-js"], "registryDependencies": ["button", "card", "badge", "separator"], "files": [{"path": "lib/stripe.ts", "type": "registry:lib"}, {"path": "lib/pricing.ts", "type": "registry:lib"}, {"path": "hooks/use-stripe.ts", "type": "registry:hook"}, {"path": "components/ui/pricing-card.tsx", "type": "registry:component"}, {"path": "components/ui/subscription-manager.tsx", "type": "registry:component"}, {"path": "actions/stripe-actions.ts", "type": "registry:lib"}, {"path": "app/api/stripe/create-checkout-session/route.ts", "type": "registry:lib", "target": "app/api/stripe/create-checkout-session/route.ts"}, {"path": "app/api/stripe/subscription/route.ts", "type": "registry:lib", "target": "app/api/stripe/subscription/route.ts"}, {"path": "app/api/stripe/cancel-subscription/route.ts", "type": "registry:lib", "target": "app/api/stripe/cancel-subscription/route.ts"}, {"path": "app/api/stripe/reactivate-subscription/route.ts", "type": "registry:lib", "target": "app/api/stripe/reactivate-subscription/route.ts"}, {"path": "app/api/stripe/webhooks/route.ts", "type": "registry:lib", "target": "app/api/stripe/webhooks/route.ts"}]}, {"name": "customer-portal", "type": "registry:component", "title": "Stripe Customer Portal Module", "description": "Self-service customer portal for managing billing, subscriptions, and payment methods", "dependencies": ["stripe", "@stripe/stripe-js"], "registryDependencies": ["button", "card"], "files": [{"path": "lib/stripe.ts", "type": "registry:lib"}, {"path": "hooks/use-stripe.ts", "type": "registry:hook"}, {"path": "actions/stripe-actions.ts", "type": "registry:lib"}, {"path": "app/api/stripe/customer-portal/route.ts", "type": "registry:lib", "target": "app/api/stripe/customer-portal/route.ts"}]}, {"name": "theme-toggle", "type": "registry:component", "title": "Theme Toggle Module", "description": "Comprehensive dark mode solution with theme provider and multiple toggle variants for Next.js applications", "dependencies": ["next-themes"], "registryDependencies": ["button", "dropdown-menu"], "files": [{"path": "components/theme-provider.tsx", "type": "registry:component"}, {"path": "components/ui/theme-toggle.tsx", "type": "registry:component"}, {"path": "components/ui/theme-toggle-dropdown.tsx", "type": "registry:component"}, {"path": "components/ui/theme-aware.tsx", "type": "registry:component"}]}, {"name": "config-module", "type": "registry:component", "title": "Supreme Toolkit Configuration", "description": "Central configuration system for managing API keys, settings, and module configurations", "dependencies": [], "registryDependencies": [], "files": [{"path": "config.tsx", "type": "registry:lib", "target": "config.tsx"}, {"path": "lib/config/auth.ts", "type": "registry:lib"}, {"path": "lib/config/stripe.ts", "type": "registry:lib"}, {"path": "lib/config/mailer.ts", "type": "registry:lib"}, {"path": "lib/config/chat.ts", "type": "registry:lib"}, {"path": "lib/config/chatbot.ts", "type": "registry:lib"}, {"path": "lib/config/support.ts", "type": "registry:lib"}, {"path": "lib/config/feedback.ts", "type": "registry:lib"}, {"path": "lib/config/image-uploader.ts", "type": "registry:lib"}, {"path": "lib/config/rich-text-editor.ts", "type": "registry:lib"}, {"path": "lib/config/waitlist.ts", "type": "registry:lib"}]}, {"name": "chat-realtime", "type": "registry:component", "title": "Real-time Chat", "description": "Complete real-time chat system with Supabase backend, message handling, presence indicators, and room management", "dependencies": ["@supabase/supabase-js", "date-fns"], "registryDependencies": ["button", "card", "input", "avatar", "scroll-area", "badge"], "files": [{"path": "lib/chat-utils.ts", "type": "registry:lib"}, {"path": "types/chat.ts", "type": "registry:lib"}, {"path": "hooks/use-chat.ts", "type": "registry:hook"}, {"path": "components/ui/chat-room.tsx", "type": "registry:component"}, {"path": "components/ui/chat-message.tsx", "type": "registry:component"}, {"path": "components/ui/chat-input.tsx", "type": "registry:component"}, {"path": "components/ui/chat-participants.tsx", "type": "registry:component"}, {"path": "actions/chat-actions.ts", "type": "registry:lib"}, {"path": "app/api/chat/messages/route.ts", "type": "registry:lib", "target": "app/api/chat/messages/route.ts"}, {"path": "app/api/chat/rooms/route.ts", "type": "registry:lib", "target": "app/api/chat/rooms/route.ts"}]}, {"name": "chatbot-gpt", "type": "registry:component", "title": "AI Chatbot", "description": "AI chatbot widget with OpenAI integration, streaming responses, conversation management, and customizable interface", "dependencies": ["date-fns"], "registryDependencies": ["button", "card", "input", "textarea", "avatar", "scroll-area", "badge"], "files": [{"path": "lib/openai-utils.ts", "type": "registry:lib"}, {"path": "types/chatbot.ts", "type": "registry:lib"}, {"path": "hooks/use-chatbot.ts", "type": "registry:hook"}, {"path": "components/ui/chatbot-widget.tsx", "type": "registry:component"}, {"path": "components/ui/chatbot-message.tsx", "type": "registry:component"}, {"path": "components/ui/chatbot-input.tsx", "type": "registry:component"}, {"path": "actions/chatbot-actions.ts", "type": "registry:lib"}, {"path": "app/api/chatbot/chat/route.ts", "type": "registry:lib", "target": "app/api/chatbot/chat/route.ts"}, {"path": "app/api/chatbot/conversations/route.ts", "type": "registry:lib", "target": "app/api/chatbot/conversations/route.ts"}]}, {"name": "support-ticket-system", "type": "registry:component", "title": "Support Tickets", "description": "Complete support ticket system with ticket creation, status management, comments, categories, and admin dashboard", "dependencies": ["date-fns"], "registryDependencies": ["button", "card", "input", "textarea", "select", "badge", "scroll-area"], "files": [{"path": "lib/ticket-utils.ts", "type": "registry:lib"}, {"path": "types/tickets.ts", "type": "registry:lib"}, {"path": "hooks/use-tickets.ts", "type": "registry:hook"}, {"path": "components/ui/ticket-form.tsx", "type": "registry:component"}, {"path": "components/ui/ticket-list.tsx", "type": "registry:component"}, {"path": "components/ui/ticket-details.tsx", "type": "registry:component"}, {"path": "components/ui/ticket-comments.tsx", "type": "registry:component"}, {"path": "actions/ticket-actions.ts", "type": "registry:lib"}, {"path": "app/api/tickets/route.ts", "type": "registry:lib", "target": "app/api/tickets/route.ts"}, {"path": "app/api/tickets/[id]/route.ts", "type": "registry:lib", "target": "app/api/tickets/[id]/route.ts"}, {"path": "app/api/tickets/[id]/comments/route.ts", "type": "registry:lib", "target": "app/api/tickets/[id]/comments/route.ts"}]}, {"name": "feedback-widget", "type": "registry:component", "title": "<PERSON><PERSON><PERSON> W<PERSON>t", "description": "User feedback collection widget with ratings, screenshots, categories, and admin dashboard for managing feedback", "dependencies": [], "registryDependencies": ["button", "card", "input", "textarea", "select", "badge"], "files": [{"path": "lib/feedback-utils.ts", "type": "registry:lib"}, {"path": "types/feedback.ts", "type": "registry:lib"}, {"path": "hooks/use-feedback.ts", "type": "registry:hook"}, {"path": "components/ui/feedback-widget.tsx", "type": "registry:component"}, {"path": "components/ui/feedback-form.tsx", "type": "registry:component"}, {"path": "components/ui/feedback-rating.tsx", "type": "registry:component"}, {"path": "components/ui/feedback-dashboard.tsx", "type": "registry:component"}, {"path": "actions/feedback-actions.ts", "type": "registry:lib"}, {"path": "app/api/feedback/route.ts", "type": "registry:lib", "target": "app/api/feedback/route.ts"}, {"path": "app/api/feedback/[id]/route.ts", "type": "registry:lib", "target": "app/api/feedback/[id]/route.ts"}]}, {"name": "image-uploader", "type": "registry:component", "title": "Image Uploader", "description": "Complete image upload system with drag & drop, progress tracking, cloud storage integration, and image management", "dependencies": [], "registryDependencies": ["button", "card", "progress", "badge"], "files": [{"path": "lib/image-utils.ts", "type": "registry:lib"}, {"path": "types/image-uploader.ts", "type": "registry:lib"}, {"path": "hooks/use-image-upload.ts", "type": "registry:hook"}, {"path": "hooks/use-image-gallery.ts", "type": "registry:hook"}, {"path": "components/ui/image-uploader.tsx", "type": "registry:component"}, {"path": "components/ui/image-dropzone.tsx", "type": "registry:component"}, {"path": "components/ui/image-preview.tsx", "type": "registry:component"}, {"path": "components/ui/image-gallery.tsx", "type": "registry:component"}, {"path": "actions/image-actions.ts", "type": "registry:lib"}, {"path": "app/api/images/upload/route.ts", "type": "registry:lib", "target": "app/api/images/upload/route.ts"}, {"path": "app/api/images/[id]/route.ts", "type": "registry:lib", "target": "app/api/images/[id]/route.ts"}]}, {"name": "rich-text-editor", "type": "registry:component", "title": "Rich Text Editor", "description": "Feature-rich WYSIWYG text editor with toolbar, formatting options, image upload, link insertion, and export capabilities", "dependencies": [], "registryDependencies": ["button", "card", "separator", "select", "popover", "input", "label", "badge"], "files": [{"path": "lib/editor-utils.ts", "type": "registry:lib"}, {"path": "types/editor.ts", "type": "registry:lib"}, {"path": "hooks/use-rich-text-editor.ts", "type": "registry:hook"}, {"path": "components/ui/rich-text-editor.tsx", "type": "registry:component"}, {"path": "components/ui/editor-toolbar.tsx", "type": "registry:component"}, {"path": "components/ui/editor-status-bar.tsx", "type": "registry:component"}]}]}