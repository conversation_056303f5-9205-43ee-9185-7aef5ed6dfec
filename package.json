{"name": "supreme-toolkit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/better-sqlite3": "^7.6.13", "@types/nodemailer": "^6.4.17", "@types/stripe": "^8.0.416", "@vercel/analytics": "^1.5.0", "better-auth": "^1.2.12", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "motion": "^12.23.0", "next": "15.3.5", "next-themes": "^0.4.6", "nodemailer": "^7.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "shiki": "^3.7.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}