/**
 * Chat Realtime Configuration
 * 
 * Configure Supabase realtime chat settings
 */

export interface ChatConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  maxMessageLength?: number;
  enableTypingIndicators?: boolean;
  enableFileUploads?: boolean;
  enableReactions?: boolean;
}

export const defaultChatConfig: Partial<ChatConfig> = {
  maxMessageLength: 1000,
  enableTypingIndicators: true,
  enableFileUploads: false,
  enableReactions: true,
};

/**
 * Get chat configuration with defaults
 */
export function getChatConfig(userConfig?: Partial<ChatConfig>): ChatConfig {
  return {
    ...defaultChatConfig,
    ...userConfig,
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  };
}

/**
 * Validate chat environment variables
 */
export function validateChatConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is required for chat module');
  }
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is required for chat module');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
