{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "auth-module", "type": "registry:component", "title": "Authentication Module", "description": "Complete authentication system with betterAuth, multiple providers (Google, GitHub, email/password), and auth guards", "dependencies": ["better-auth", "better-sqlite3"], "devDependencies": ["@types/better-sqlite3"], "registryDependencies": ["button", "input", "label", "card", "avatar", "separator"], "files": [{"path": "lib/auth.ts", "content": "/**\n * Supreme Toolkit - Better Auth Configuration\n * \n * This file configures betterAuth for the Supreme Toolkit auth module.\n * It provides a complete authentication system with multiple providers.\n */\n\nimport { betterAuth } from \"better-auth\";\nimport { getModuleConfig } from \"@/config\";\nimport Database from \"better-sqlite3\";\nimport path from \"path\";\n\n// Get auth configuration from Supreme Toolkit config\nfunction getAuthConfig() {\n  try {\n    return getModuleConfig('auth');\n  } catch {\n    return {\n      providers: ['email'] as ('google' | 'github' | 'email')[],\n      sessionDuration: 60 * 60 * 24 * 30 // 30 days\n    };\n  }\n}\n\nconst authConfig = getAuthConfig();\n\n// Create SQLite database for demo purposes\n// In production, you'd use PostgreSQL, MySQL, or another database\nconst dbPath = path.join(process.cwd(), 'auth.db');\nconst database = new Database(dbPath);\n\nexport const auth = betterAuth({\n  // Database configuration\n  database,\n  \n  // Base URL for the application\n  baseURL: process.env.BETTER_AUTH_URL || process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\",\n  \n  // Secret for encryption and signing\n  secret: process.env.BETTER_AUTH_SECRET || \"your-secret-key-change-in-production\",\n  \n  // Email and password authentication\n  emailAndPassword: {\n    enabled: true,\n    requireEmailVerification: false, // Set to true in production\n  },\n  \n  // Social providers configuration\n  socialProviders: {\n    // Google OAuth\n    ...(authConfig?.providers?.includes('google') && {\n      google: {\n        clientId: process.env.GOOGLE_CLIENT_ID as string,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,\n      }\n    }),\n    \n    // GitHub OAuth\n    ...(authConfig?.providers?.includes('github') && {\n      github: {\n        clientId: process.env.GITHUB_CLIENT_ID as string,\n        clientSecret: process.env.GITHUB_CLIENT_SECRET as string,\n      }\n    }),\n  },\n  \n  // Session configuration\n  session: {\n    expiresIn: authConfig?.sessionDuration || 60 * 60 * 24 * 30, // 30 days\n    updateAge: 60 * 60 * 24, // Update session every 24 hours\n  },\n  \n  // User configuration\n  user: {\n    additionalFields: {\n      role: {\n        type: \"string\",\n        defaultValue: \"user\",\n      },\n      avatar: {\n        type: \"string\",\n        required: false,\n      },\n    },\n  },\n  \n  // Advanced configuration\n  advanced: {\n    generateId: () => {\n      // Generate a custom ID for users\n      return crypto.randomUUID();\n    },\n  },\n});\n\n// Export types for use in other files\nexport type AuthUser = typeof auth.$Infer.User;\nexport type AuthSession = typeof auth.$Infer.Session;\n", "type": "registry:lib"}, {"path": "lib/auth-client.ts", "content": "/**\n * Supreme Toolkit - Better Auth Client\n * \n * This file configures the betterAuth client for React components.\n * It provides hooks and methods for client-side authentication.\n */\n\nimport { createAuthClient } from \"better-auth/react\";\n\n// Create the auth client\nexport const authClient = createAuthClient({\n  baseURL: process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\",\n});\n\n// Export commonly used methods and hooks\nexport const {\n  signIn,\n  signUp,\n  signOut,\n  useSession,\n  getSession,\n} = authClient;\n", "type": "registry:lib"}, {"path": "hooks/use-auth.ts", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { authClient } from \"@/lib/auth-client\";\nimport { onUserSignup, onUserLogin, onUserLogout } from \"@/actions/auth-actions\";\n\ninterface UseAuthOptions {\n  onSuccess?: (data: any) => void;\n  onError?: (error: string) => void;\n}\n\ninterface SignInParams {\n  email: string;\n  password: string;\n}\n\ninterface SignUpParams {\n  name: string;\n  email: string;\n  password: string;\n}\n\nexport function useAuth(options: UseAuthOptions = {}) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Get session from authClient\n  const { data: session, isPending: sessionLoading } = authClient.useSession();\n\n  const signIn = async (params: SignInParams) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signIn.email({\n        email: params.email,\n        password: params.password,\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign in failed');\n      }\n\n      // Call server action for custom logic\n      if (result.data?.user) {\n        await onUserLogin({\n          user: result.data.user,\n          provider: 'email',\n          isFirstLogin: false, // You might want to track this\n          timestamp: new Date(),\n        });\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signUp = async (params: SignUpParams) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signUp.email({\n        email: params.email,\n        password: params.password,\n        name: params.name,\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign up failed');\n      }\n\n      // Call server action for custom logic\n      if (result.data?.user) {\n        await onUserSignup({\n          user: result.data.user,\n          provider: 'email',\n          metadata: { name: params.name },\n          timestamp: new Date(),\n        });\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signInWithProvider = async (provider: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await authClient.signIn.social({\n        provider: provider as any,\n        callbackURL: window.location.origin + '/dashboard',\n      });\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Social sign in failed');\n      }\n\n      // Note: For social sign-in, the callback will handle the server action\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signOut = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Call server action before signing out\n      if (session?.user) {\n        await onUserLogout({\n          userId: session.user.id,\n          sessionId: session.session.id,\n          timestamp: new Date(),\n        });\n      }\n\n      const result = await authClient.signOut();\n\n      if (result.error) {\n        throw new Error(result.error.message || 'Sign out failed');\n      }\n\n      options.onSuccess?.(result.data);\n      return { success: true, data: result.data };\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const reset = () => {\n    setError(null);\n  };\n\n  return {\n    // State\n    session,\n    user: session?.user || null,\n    isAuthenticated: !!session?.user,\n    isLoading: isLoading || sessionLoading,\n    error,\n\n    // Actions\n    signIn,\n    signUp,\n    signInWithProvider,\n    signOut,\n    reset,\n  };\n}\n", "type": "registry:hook"}]}