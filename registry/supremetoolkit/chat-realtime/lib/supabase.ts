import { createClient } from '@supabase/supabase-js';
import { getModuleConfig } from '@/config';

// ============================================================================
// SUPABASE CLIENT SETUP
// ============================================================================

const config = getModuleConfig('chat');

if (!config?.supabaseUrl || !config?.supabaseAnonKey) {
  throw new Error('Supabase configuration is missing. Please add SUPABASE_URL and SUPABASE_ANON_KEY to your config.');
}

export const supabase = createClient(
  config.supabaseUrl,
  config.supabaseAnonKey
);

// ============================================================================
// DATABASE TYPES
// ============================================================================

export interface ChatRoom {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  created_by: string;
  created_at: string;
  updated_at: string;
  participants?: string[];
  last_message_at?: string;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  user_id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  edited_at?: string;
  reply_to?: string;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export interface ChatUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away';
  last_seen: string;
}

// ============================================================================
// SUPABASE HELPERS
// ============================================================================

export const chatQueries = {
  // Room queries
  getRooms: () => 
    supabase
      .from('chat_rooms')
      .select('*')
      .order('last_message_at', { ascending: false }),

  getRoom: (roomId: string) =>
    supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .single(),

  createRoom: (room: Omit<ChatRoom, 'id' | 'created_at' | 'updated_at'>) =>
    supabase
      .from('chat_rooms')
      .insert(room)
      .select()
      .single(),

  // Message queries
  getMessages: (roomId: string, limit = 50) =>
    supabase
      .from('chat_messages')
      .select(`
        *,
        user:chat_users(id, name, avatar)
      `)
      .eq('room_id', roomId)
      .order('created_at', { ascending: false })
      .limit(limit),

  sendMessage: (message: Omit<ChatMessage, 'id' | 'created_at' | 'updated_at'>) =>
    supabase
      .from('chat_messages')
      .insert(message)
      .select(`
        *,
        user:chat_users(id, name, avatar)
      `)
      .single(),

  // User queries
  getUsers: () =>
    supabase
      .from('chat_users')
      .select('*'),

  updateUserStatus: (userId: string, status: ChatUser['status']) =>
    supabase
      .from('chat_users')
      .update({ status, last_seen: new Date().toISOString() })
      .eq('id', userId),
};

// ============================================================================
// REALTIME SUBSCRIPTIONS
// ============================================================================

export const subscribeToRoom = (
  roomId: string,
  onMessage: (message: ChatMessage) => void,
  onError?: (error: any) => void
) => {
  return supabase
    .channel(`room:${roomId}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_messages',
        filter: `room_id=eq.${roomId}`,
      },
      (payload) => {
        onMessage(payload.new as ChatMessage);
      }
    )
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'chat_messages',
        filter: `room_id=eq.${roomId}`,
      },
      (payload) => {
        onMessage(payload.new as ChatMessage);
      }
    )
    .subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log(`Subscribed to room: ${roomId}`);
      } else if (status === 'CHANNEL_ERROR') {
        console.error(`Error subscribing to room: ${roomId}`);
        onError?.(status);
      }
    });
};

export const subscribeToUserPresence = (
  roomId: string,
  userId: string,
  userInfo: { name: string; avatar?: string },
  onPresenceChange: (users: any[]) => void
) => {
  return supabase
    .channel(`presence:${roomId}`)
    .on('presence', { event: 'sync' }, () => {
      const state = supabase.getChannels()[0].presenceState();
      const users = Object.values(state).flat();
      onPresenceChange(users);
    })
    .on('presence', { event: 'join' }, ({ key, newPresences }) => {
      console.log('User joined:', key, newPresences);
    })
    .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
      console.log('User left:', key, leftPresences);
    })
    .subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        await supabase.channel(`presence:${roomId}`).track({
          user_id: userId,
          ...userInfo,
          online_at: new Date().toISOString(),
        });
      }
    });
};
