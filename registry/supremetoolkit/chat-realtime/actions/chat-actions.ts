"use server";

import { chatQueries } from '../lib/supabase';
import type { ChatRoom, ChatMessage, CreateRoomRequest, SendMessageRequest } from '../types';

// ============================================================================
// CHAT SERVER ACTIONS
// ============================================================================

/**
 * Create a new chat room
 */
export async function createChatRoom(
  userId: string,
  roomData: CreateRoomRequest
): Promise<{
  success: boolean;
  room?: ChatRoom;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required',
      };
    }

    if (!roomData.name?.trim()) {
      return {
        success: false,
        error: 'Room name is required',
      };
    }

    const { data, error } = await chatQueries.createRoom({
      name: roomData.name.trim(),
      description: roomData.description?.trim(),
      type: roomData.type,
      created_by: userId,
      participants: roomData.participants || [userId],
    });

    if (error) {
      console.error('Error creating room:', error);
      return {
        success: false,
        error: 'Failed to create room',
      };
    }

    return {
      success: true,
      room: data,
    };
  } catch (error) {
    console.error('Error in createChatRoom:', error);
    return {
      success: false,
      error: 'Failed to create room',
    };
  }
}

/**
 * Send a message to a chat room
 */
export async function sendChatMessage(
  userId: string,
  messageData: SendMessageRequest
): Promise<{
  success: boolean;
  message?: ChatMessage;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required',
      };
    }

    if (!messageData.content?.trim()) {
      return {
        success: false,
        error: 'Message content is required',
      };
    }

    if (!messageData.room_id) {
      return {
        success: false,
        error: 'Room ID is required',
      };
    }

    const { data, error } = await chatQueries.sendMessage({
      room_id: messageData.room_id,
      user_id: userId,
      content: messageData.content.trim(),
      type: messageData.type || 'text',
      reply_to: messageData.reply_to,
      metadata: messageData.metadata,
    });

    if (error) {
      console.error('Error sending message:', error);
      return {
        success: false,
        error: 'Failed to send message',
      };
    }

    return {
      success: true,
      message: data,
    };
  } catch (error) {
    console.error('Error in sendChatMessage:', error);
    return {
      success: false,
      error: 'Failed to send message',
    };
  }
}

/**
 * Get chat rooms for a user
 */
export async function getUserChatRooms(userId: string): Promise<{
  success: boolean;
  rooms?: ChatRoom[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required',
      };
    }

    const { data, error } = await chatQueries.getRooms();

    if (error) {
      console.error('Error fetching rooms:', error);
      return {
        success: false,
        error: 'Failed to fetch rooms',
      };
    }

    // Filter rooms based on user access
    const accessibleRooms = data?.filter(room => {
      // Public rooms are accessible to everyone
      if (room.type === 'public') return true;
      
      // Private rooms and direct messages need participant check
      if (room.participants) {
        return room.participants.includes(userId);
      }
      
      // If no participants array, check if user created the room
      return room.created_by === userId;
    }) || [];

    return {
      success: true,
      rooms: accessibleRooms,
    };
  } catch (error) {
    console.error('Error in getUserChatRooms:', error);
    return {
      success: false,
      error: 'Failed to fetch rooms',
    };
  }
}

/**
 * Get messages for a chat room
 */
export async function getChatMessages(
  roomId: string,
  limit = 50
): Promise<{
  success: boolean;
  messages?: ChatMessage[];
  error?: string;
}> {
  try {
    if (!roomId) {
      return {
        success: false,
        error: 'Room ID is required',
      };
    }

    const { data, error } = await chatQueries.getMessages(roomId, limit);

    if (error) {
      console.error('Error fetching messages:', error);
      return {
        success: false,
        error: 'Failed to fetch messages',
      };
    }

    return {
      success: true,
      messages: data?.reverse() || [], // Reverse to show oldest first
    };
  } catch (error) {
    console.error('Error in getChatMessages:', error);
    return {
      success: false,
      error: 'Failed to fetch messages',
    };
  }
}

/**
 * Join a chat room
 */
export async function joinChatRoom(
  userId: string,
  roomId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    if (!userId || !roomId) {
      return {
        success: false,
        error: 'User ID and Room ID are required',
      };
    }

    // Get room details first
    const { data: room, error: fetchError } = await chatQueries.getRoom(roomId);
    
    if (fetchError || !room) {
      return {
        success: false,
        error: 'Room not found',
      };
    }

    // Check if user is already a participant
    const participants = room.participants || [];
    if (participants.includes(userId)) {
      return { success: true }; // Already a participant
    }

    // Add user to participants
    const updatedParticipants = [...participants, userId];
    
    const { error: updateError } = await chatQueries.supabase
      .from('chat_rooms')
      .update({ participants: updatedParticipants })
      .eq('id', roomId);

    if (updateError) {
      console.error('Error joining room:', updateError);
      return {
        success: false,
        error: 'Failed to join room',
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in joinChatRoom:', error);
    return {
      success: false,
      error: 'Failed to join room',
    };
  }
}

/**
 * Leave a chat room
 */
export async function leaveChatRoom(
  userId: string,
  roomId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    if (!userId || !roomId) {
      return {
        success: false,
        error: 'User ID and Room ID are required',
      };
    }

    // Get room details first
    const { data: room, error: fetchError } = await chatQueries.getRoom(roomId);
    
    if (fetchError || !room) {
      return {
        success: false,
        error: 'Room not found',
      };
    }

    // Remove user from participants
    const participants = room.participants || [];
    const updatedParticipants = participants.filter(id => id !== userId);
    
    const { error: updateError } = await chatQueries.supabase
      .from('chat_rooms')
      .update({ participants: updatedParticipants })
      .eq('id', roomId);

    if (updateError) {
      console.error('Error leaving room:', updateError);
      return {
        success: false,
        error: 'Failed to leave room',
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in leaveChatRoom:', error);
    return {
      success: false,
      error: 'Failed to leave room',
    };
  }
}
