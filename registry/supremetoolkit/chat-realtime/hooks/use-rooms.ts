"use client";

import { useState, useEffect, useCallback } from 'react';
import { chatQueries } from '../lib/supabase';
import type { ChatRoom, UseRoomsOptions, UseRoomsReturn } from '../types';

export function useRooms({
  userId,
  onError,
}: UseRoomsOptions): UseRoomsReturn {
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load rooms
  const loadRooms = useCallback(async () => {
    try {
      setError(null);
      setIsLoading(true);
      
      const { data, error: fetchError } = await chatQueries.getRooms();
      
      if (fetchError) {
        throw fetchError;
      }

      if (data) {
        // Filter rooms based on user access
        const accessibleRooms = data.filter(room => {
          // Public rooms are accessible to everyone
          if (room.type === 'public') return true;
          
          // Private rooms and direct messages need participant check
          if (room.participants) {
            return room.participants.includes(userId);
          }
          
          // If no participants array, check if user created the room
          return room.created_by === userId;
        });
        
        setRooms(accessibleRooms);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load rooms';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [userId, onError]);

  // Create room
  const createRoom = useCallback(async (
    roomData: Omit<ChatRoom, 'id' | 'created_at' | 'updated_at'>
  ): Promise<ChatRoom | null> => {
    try {
      setError(null);
      
      const { data, error: createError } = await chatQueries.createRoom({
        ...roomData,
        created_by: userId,
      });

      if (createError) {
        throw createError;
      }

      if (data) {
        // Add to local state
        setRooms(prev => [data, ...prev]);
        return data;
      }
      
      return null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create room';
      setError(errorMessage);
      onError?.(errorMessage);
      return null;
    }
  }, [userId, onError]);

  // Join room
  const joinRoom = useCallback(async (roomId: string) => {
    try {
      setError(null);
      
      // Get room details first
      const { data: room, error: fetchError } = await chatQueries.getRoom(roomId);
      
      if (fetchError) {
        throw fetchError;
      }

      if (!room) {
        throw new Error('Room not found');
      }

      // Check if user is already a participant
      const participants = room.participants || [];
      if (participants.includes(userId)) {
        return; // Already a participant
      }

      // Add user to participants
      const updatedParticipants = [...participants, userId];
      
      const { error: updateError } = await chatQueries.supabase
        .from('chat_rooms')
        .update({ participants: updatedParticipants })
        .eq('id', roomId);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setRooms(prev => 
        prev.map(r => 
          r.id === roomId 
            ? { ...r, participants: updatedParticipants }
            : r
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to join room';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [userId, onError]);

  // Leave room
  const leaveRoom = useCallback(async (roomId: string) => {
    try {
      setError(null);
      
      // Get room details first
      const { data: room, error: fetchError } = await chatQueries.getRoom(roomId);
      
      if (fetchError) {
        throw fetchError;
      }

      if (!room) {
        throw new Error('Room not found');
      }

      // Remove user from participants
      const participants = room.participants || [];
      const updatedParticipants = participants.filter(id => id !== userId);
      
      const { error: updateError } = await chatQueries.supabase
        .from('chat_rooms')
        .update({ participants: updatedParticipants })
        .eq('id', roomId);

      if (updateError) {
        throw updateError;
      }

      // Update local state - remove room if user was the only participant
      setRooms(prev => {
        if (updatedParticipants.length === 0 && room.created_by === userId) {
          // Remove room entirely if creator leaves and no other participants
          return prev.filter(r => r.id !== roomId);
        } else {
          // Update participants list
          return prev.map(r => 
            r.id === roomId 
              ? { ...r, participants: updatedParticipants }
              : r
          );
        }
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to leave room';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [userId, onError]);

  // Refresh rooms
  const refreshRooms = useCallback(async () => {
    await loadRooms();
  }, [loadRooms]);

  // Load rooms on mount
  useEffect(() => {
    if (userId) {
      loadRooms();
    }
  }, [userId, loadRooms]);

  return {
    rooms,
    isLoading,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    refreshRooms,
  };
}
