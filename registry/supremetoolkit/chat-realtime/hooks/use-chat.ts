"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase, chatQueries, subscribeToRoom, subscribeToUserPresence } from '../lib/supabase';
import type { ChatMessage, ChatPresence, UseChatOptions, UseChatReturn } from '../types';

export function useChat({
  roomId,
  userId,
  userInfo,
  onError,
}: UseChatOptions): UseChatReturn {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [onlineUsers, setOnlineUsers] = useState<ChatPresence[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  
  const subscriptionRef = useRef<any>(null);
  const presenceRef = useRef<any>(null);
  const offsetRef = useRef(0);

  // Load initial messages
  const loadMessages = useCallback(async (offset = 0, limit = 50) => {
    try {
      setError(null);
      
      const { data, error: fetchError } = await chatQueries.getMessages(roomId, limit);
      
      if (fetchError) {
        throw fetchError;
      }

      if (data) {
        const sortedMessages = data.reverse(); // Reverse to show oldest first
        
        if (offset === 0) {
          setMessages(sortedMessages);
        } else {
          setMessages(prev => [...sortedMessages, ...prev]);
        }
        
        setHasMoreMessages(data.length === limit);
        offsetRef.current = offset + data.length;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load messages';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [roomId, onError]);

  // Load more messages (pagination)
  const loadMoreMessages = useCallback(async () => {
    if (!hasMoreMessages || isLoading) return;
    
    setIsLoading(true);
    await loadMessages(offsetRef.current);
  }, [hasMoreMessages, isLoading, loadMessages]);

  // Send message
  const sendMessage = useCallback(async (content: string, type: ChatMessage['type'] = 'text') => {
    if (!content.trim()) return;

    try {
      setError(null);
      
      const { data, error: sendError } = await chatQueries.sendMessage({
        room_id: roomId,
        user_id: userId,
        content: content.trim(),
        type,
      });

      if (sendError) {
        throw sendError;
      }

      // Message will be added via real-time subscription
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [roomId, userId, onError]);

  // Edit message
  const editMessage = useCallback(async (messageId: string, content: string) => {
    if (!content.trim()) return;

    try {
      setError(null);
      
      const { error: editError } = await supabase
        .from('chat_messages')
        .update({ 
          content: content.trim(),
          edited_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .eq('user_id', userId); // Only allow editing own messages

      if (editError) {
        throw editError;
      }

      // Update local state
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, content: content.trim(), edited_at: new Date().toISOString() }
            : msg
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to edit message';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [userId, onError]);

  // Delete message
  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      setError(null);
      
      const { error: deleteError } = await supabase
        .from('chat_messages')
        .delete()
        .eq('id', messageId)
        .eq('user_id', userId); // Only allow deleting own messages

      if (deleteError) {
        throw deleteError;
      }

      // Update local state
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete message';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  }, [userId, onError]);

  // Handle new messages from subscription
  const handleNewMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => {
      // Check if message already exists (avoid duplicates)
      const exists = prev.some(msg => msg.id === message.id);
      if (exists) return prev;
      
      return [...prev, message];
    });
  }, []);

  // Handle presence changes
  const handlePresenceChange = useCallback((users: ChatPresence[]) => {
    setOnlineUsers(users);
  }, []);

  // Setup subscriptions
  useEffect(() => {
    if (!roomId || !userId) return;

    // Subscribe to messages
    subscriptionRef.current = subscribeToRoom(
      roomId,
      handleNewMessage,
      (error) => {
        setIsConnected(false);
        const errorMessage = 'Real-time connection error';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    );

    // Subscribe to presence
    presenceRef.current = subscribeToUserPresence(
      roomId,
      userId,
      userInfo,
      handlePresenceChange
    );

    setIsConnected(true);

    // Cleanup subscriptions
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
      if (presenceRef.current) {
        supabase.removeChannel(presenceRef.current);
      }
      setIsConnected(false);
    };
  }, [roomId, userId, userInfo, handleNewMessage, handlePresenceChange, onError]);

  // Load initial messages
  useEffect(() => {
    if (roomId) {
      setMessages([]);
      offsetRef.current = 0;
      setIsLoading(true);
      loadMessages();
    }
  }, [roomId, loadMessages]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    editMessage,
    deleteMessage,
    loadMoreMessages,
    hasMoreMessages,
    onlineUsers,
    isConnected,
  };
}
