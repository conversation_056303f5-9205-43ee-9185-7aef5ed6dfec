/**
 * Supreme Toolkit Configuration
 *
 * Configure your Supreme Toolkit modules here.
 * Only include the modules you're using in your project.
 *
 * Each module has its own configuration interface.
 * Add environment variables to your .env.local file.
 */

// ============================================================================
// MODULE CONFIGURATIONS
// ============================================================================

/**
 * Authentication Module Configuration
 * Requires: better-auth setup
 */
export const authConfig = {
  providers: ['google', 'email', 'github'] as const,
  // Add your auth configuration here
};

/**
 * Stripe Payment Configuration
 * Requires: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY
 */
export const stripeConfig = {
  // Add your Stripe configuration here
  // successUrl: '/payment/success',
  // cancelUrl: '/payment/cancel',
};

/**
 * Mailer Configuration
 * Requires: RESEND_API_KEY or SMTP credentials
 */
export const mailerConfig = {
  // Add your mailer configuration here
  // fromEmail: '<EMAIL>',
  // fromName: 'Your App',
};

/**
 * Waitlist Configuration
 */
export const waitlistConfig = {
  successRedirect: '/thanks',
  emailNotifications: true,
};

/**
 * Chat Realtime Configuration
 * Requires: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY
 */
export const chatConfig = {
  // Add your chat configuration here
  // maxMessageLength: 1000,
  // enableFileUploads: true,
};

/**
 * Chatbot GPT Configuration
 * Requires: OPENAI_API_KEY
 */
export const chatbotConfig = {
  // Add your chatbot configuration here
  // model: 'gpt-4',
  // maxTokens: 1000,
  // temperature: 0.7,
};

/**
 * Support Ticket System Configuration
 */
export const supportConfig = {
  defaultPriority: 'medium' as const,
  emailNotifications: true,
};

/**
 * Feedback Widget Configuration
 */
export const feedbackConfig = {
  categories: ['bug', 'feature', 'improvement', 'other'],
  enableScreenshots: true,
  emailNotifications: true,
};

/**
 * Image Uploader Configuration
 * Requires: CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET (for Cloudinary)
 */
export const imageUploaderConfig = {
  // Add your image uploader configuration here
  // provider: 'cloudinary',
  // maxFileSize: 10, // MB
  // allowedFormats: ['jpg', 'jpeg', 'png', 'webp'],
};

/**
 * Rich Text Editor Configuration
 */
export const richTextEditorConfig = {
  autoSave: true,
  autoSaveInterval: 30000, // 30 seconds
  // Add your editor configuration here
};

// ============================================================================
// ENVIRONMENT VARIABLES REFERENCE
// ============================================================================

/**
 * Required Environment Variables by Module:
 *
 * AUTH MODULE:
 * - BETTER_AUTH_SECRET
 * - GOOGLE_CLIENT_ID (if using Google)
 * - GOOGLE_CLIENT_SECRET (if using Google)
 * - GITHUB_CLIENT_ID (if using GitHub)
 * - GITHUB_CLIENT_SECRET (if using GitHub)
 *
 * STRIPE MODULES:
 * - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
 * - STRIPE_SECRET_KEY
 * - STRIPE_WEBHOOK_SECRET
 *
 * MAILER MODULE:
 * - RESEND_API_KEY (for Resend)
 * - SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS (for Nodemailer)
 *
 * CHAT REALTIME:
 * - NEXT_PUBLIC_SUPABASE_URL
 * - NEXT_PUBLIC_SUPABASE_ANON_KEY
 *
 * CHATBOT GPT:
 * - OPENAI_API_KEY
 *
 * IMAGE UPLOADER:
 * - CLOUDINARY_CLOUD_NAME (for Cloudinary)
 * - CLOUDINARY_API_KEY (for Cloudinary)
 * - CLOUDINARY_API_SECRET (for Cloudinary)
 */
